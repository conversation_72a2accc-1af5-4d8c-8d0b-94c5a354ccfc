// Reset database script - يمكن استخدامه لإعادة تعيين قاعدة البيانات

function resetDatabase() {
    try {
        // Clear existing data
        localStorage.removeItem('dental_lab_data');
        
        // Create fresh database
        const initialData = {
            users: [
                { 
                    id: 1, 
                    username: 'admin', 
                    password: 'admin123', 
                    role: 'admin', 
                    fullName: 'مدير النظام',
                    email: '<EMAIL>',
                    phone: '',
                    isActive: true,
                    lastLogin: null,
                    created_at: new Date().toISOString() 
                }
            ],
            employees: [],
            doctors: [],
            prostheses: [],
            expenses: [],
            payments: [],
            employee_commissions: [],
            lab_settings: {
                labName: 'معمل الأسنان المتخصص',
                address: 'العنوان',
                phones: ['***********', '03-5750974', '***********'],
                email: '',
                website: '',
                logo: '',
                created_at: new Date().toISOString()
            },
            nextId: {
                users: 2,
                employees: 1,
                doctors: 1,
                prostheses: 1,
                expenses: 1,
                payments: 1,
                employee_commissions: 1
            }
        };
        
        localStorage.setItem('dental_lab_data', JSON.stringify(initialData));
        
        console.log('Database reset successfully');
        alert('تم إعادة تعيين قاعدة البيانات بنجاح!\nيمكنك الآن تسجيل الدخول بـ:\nاسم المستخدم: admin\nكلمة المرور: admin123');
        
        // Reload page
        location.reload();
        
        return true;
    } catch (error) {
        console.error('Error resetting database:', error);
        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات: ' + error.message);
        return false;
    }
}

// Auto-run if called directly
if (typeof window !== 'undefined') {
    window.resetDatabase = resetDatabase;
}
