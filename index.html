<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة معمل الأسنان</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div id="main-logo-container" class="main-logo" style="display: none;"></div>
            <div class="header-title">
                <h1>نظام إدارة معمل الأسنان</h1>
            </div>
            <div id="user-info"></div>
        </div>
    </header>
    
    <nav id="main-nav">
        <!-- Navigation will be populated based on user role -->
    </nav>
    
    <main id="content-area">
        <!-- Main content will be loaded dynamically -->
        <div id="login-container">
            <h2>تسجيل الدخول</h2>
            <form id="login-form">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" required>

                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" required>

                <div class="remember-me-container">
                    <label class="remember-me-label">
                        <input type="checkbox" id="remember-me">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                </div>

                <button type="submit">دخول</button>
            </form>

            <div class="reset-section" style="margin-top: 2rem; text-align: center; padding: 1rem; background: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
                <p style="margin: 0 0 1rem 0; color: #856404; font-size: 0.9rem;">
                    إذا واجهت مشكلة في تسجيل الدخول، يمكنك إعادة تعيين قاعدة البيانات:
                </p>
                <button type="button" onclick="resetDatabase()" style="background: #ffc107; color: #212529; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                    إعادة تعيين قاعدة البيانات
                </button>
            </div>
        </div>
    </main>
    
    <footer>
        <p>نظام إدارة معمل الأسنان &copy; 2025</p>
    </footer>

    <!-- Doctor Price List Modal -->
    <div id="doctor-price-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <h3 id="doctor-price-modal-title">قائمة أسعار الطبيب</h3>
                <span class="close" onclick="closeDoctorPriceModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="doctor-price-form">
                    <div id="price-list-container">
                        <!-- Price list will be generated here -->
                    </div>
                    <div class="modal-actions" style="margin-top: 20px; text-align: center;">
                        <button type="button" class="btn btn-primary" onclick="saveDoctorPrices()">💾 حفظ الأسعار</button>
                        <button type="button" class="btn btn-secondary" onclick="closeDoctorPriceModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- XLSX library for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Using simple localStorage database instead of SQL.js for better compatibility -->
    <script src="database-simple.js"></script>
    <script src="reset-database.js"></script>
    <script src="script.js"></script>
</body>
</html>