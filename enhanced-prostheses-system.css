/**
 * أنماط نظام التركيبات المتقدم
 * Enhanced Prostheses System Styles
 */

/* الحاوي الرئيسي */
.enhanced-prostheses-system {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    direction: rtl;
}

/* رأس النظام */
.system-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    flex-wrap: wrap;
    gap: 1rem;
}

.system-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.system-title i {
    color: #667eea;
}

.system-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* إحصائيات التركيبات */
.prostheses-stats {
    margin-bottom: 2rem;
}

/* التبويبات الرئيسية */
.system-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e2e8f0;
    background: #f7fafc;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #718096;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
    justify-content: center;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.tab-btn:hover {
    color: #4a5568;
    background: rgba(255, 255, 255, 0.5);
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* البحث والفلاتر */
.search-filters-section {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-bar {
    flex: 1;
    min-width: 300px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-group i {
    position: absolute;
    right: 1rem;
    color: #a0aec0;
    font-size: 1.1rem;
}

.search-input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filters-group select,
.filters-group input {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filters-group select:focus,
.filters-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.view-options .btn.active {
    background: #667eea;
    color: white;
}

/* شبكة التركيبات */
.prostheses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
}

/* بطاقة التركيبة */
.prosthesis-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-top: 4px solid var(--status-color);
}

.prosthesis-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* ألوان الحالات */
.prosthesis-card.status-pending { --status-color: #ed8936; }
.prosthesis-card.status-progress { --status-color: #4299e1; }
.prosthesis-card.status-quality { --status-color: #9f7aea; }
.prosthesis-card.status-completed { --status-color: #48bb78; }
.prosthesis-card.status-delivered { --status-color: #38a169; }
.prosthesis-card.status-cancelled { --status-color: #f56565; }

/* ألوان الأولوية */
.prosthesis-card.priority-urgent::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 30px 30px 0;
    border-color: transparent #f56565 transparent transparent;
}

.prosthesis-card.priority-high::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 30px 30px 0;
    border-color: transparent #ed8936 transparent transparent;
}

.prosthesis-card.overdue {
    border-right: 4px solid #f56565;
    background: linear-gradient(135deg, #fff 0%, #fed7d7 100%);
}

/* رأس بطاقة التركيبة */
.prosthesis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.prosthesis-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.25rem 0;
}

.prosthesis-type {
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0 0 0.25rem 0;
}

.prosthesis-patient {
    color: #718096;
    font-size: 0.85rem;
    margin: 0;
}

.prosthesis-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-pending {
    background: #fef5e7;
    color: #d69e2e;
}

.status-badge.status-progress {
    background: #bee3f8;
    color: #3182ce;
}

.status-badge.status-quality {
    background: #e9d8fd;
    color: #805ad5;
}

.status-badge.status-completed {
    background: #c6f6d5;
    color: #38a169;
}

.status-badge.status-delivered {
    background: #c6f6d5;
    color: #2f855a;
}

.status-badge.status-cancelled {
    background: #fed7d7;
    color: #e53e3e;
}

.priority-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.priority-badge.priority-urgent {
    background: #fed7d7;
    color: #e53e3e;
}

.priority-badge.priority-high {
    background: #fef5e7;
    color: #d69e2e;
}

/* مخطط الأسنان المصغر */
.mini-tooth-diagram {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    text-align: center;
}

.mini-diagram {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.mini-jaw {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mini-section {
    width: 40px;
    height: 30px;
    background: #667eea;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.mini-separator {
    font-size: 1.5rem;
    color: #a0aec0;
    font-weight: bold;
}

.mini-diagram-empty {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.9rem;
}

/* شريط التقدم */
.progress-section {
    margin-bottom: 1.5rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-label {
    font-weight: 600;
    color: #4a5568;
}

.progress-percentage {
    font-weight: 700;
    color: #667eea;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.current-stage {
    font-size: 0.85rem;
    color: #718096;
    text-align: center;
}

/* معلومات التوقيت */
.timing-info {
    margin-bottom: 1.5rem;
}

.timing-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #4a5568;
}

.timing-item i {
    color: #a0aec0;
    width: 16px;
    text-align: center;
}

.timing-item.overdue {
    color: #e53e3e;
    font-weight: 600;
}

.timing-item.overdue i {
    color: #e53e3e;
}

/* معلومات إضافية */
.additional-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.info-item i {
    font-size: 1.2rem;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.info-item span {
    font-size: 0.85rem;
    color: #4a5568;
    font-weight: 500;
}

/* إجراءات التركيبة */
.prosthesis-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    white-space: nowrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-1px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #a0aec0;
    grid-column: 1 / -1;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    color: #e2e8f0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #718096;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 1024px) {
    .prostheses-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .enhanced-prostheses-system {
        padding: 1rem;
    }
    
    .system-header {
        flex-direction: column;
        text-align: center;
    }
    
    .search-filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        min-width: auto;
    }
    
    .filters-group {
        justify-content: center;
    }
    
    .prostheses-grid {
        grid-template-columns: 1fr;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        min-width: auto;
        flex: 1;
    }
    
    .prosthesis-actions {
        justify-content: center;
    }
    
    .additional-info {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .system-title {
        font-size: 2rem;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .prosthesis-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .prosthesis-status {
        align-items: flex-start;
        flex-direction: row;
        gap: 0.5rem;
    }
}
