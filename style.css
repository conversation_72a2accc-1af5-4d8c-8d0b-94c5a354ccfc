/* Global RTL styles for Arabic interface */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #ffffff;
    color: #333;
    line-height: 1.6;
}

header {
    background: linear-gradient(to right, #1a6fc4, #0d47a1);
    color: white;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

header h1 {
    margin-bottom: 0.5rem;
}

#user-info {
    font-size: 0.9rem;
}

nav {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

nav ul {
    display: flex;
    list-style: none;
    justify-content: center;
}

nav li {
    margin-left: 1.5rem;
}

nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

nav a:hover {
    background-color: #e3f2fd;
}

main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

#login-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    max-width: 400px;
    margin: 0 auto;
}

#login-container h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #0d47a1;
}

form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

form input {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

form button {
    width: 100%;
    padding: 0.75rem;
    background-color: #0d47a1;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

form button:hover {
    background-color: #1a6fc4;
}

/* Remember Me Checkbox Styles */
.remember-me-container {
    margin: 1rem 0;
    text-align: center;
}

.remember-me-label {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    user-select: none;
    gap: 0.5rem;
}

.remember-me-label input[type="checkbox"] {
    width: auto;
    margin: 0;
    margin-left: 0.5rem;
    transform: scale(1.2);
    accent-color: #0d47a1;
}

.remember-me-label:hover {
    color: #0d47a1;
}

/* Custom checkbox styling */
.remember-me-label input[type="checkbox"]:checked {
    background-color: #0d47a1;
    border-color: #0d47a1;
}

.remember-me-label input[type="checkbox"]:focus {
    outline: 2px solid #0d47a1;
    outline-offset: 2px;
}

footer {
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    margin-top: 2rem;
    border-top: 1px solid #e9ecef;
}

/* Dashboard styles */
.dashboard {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    padding: 2rem;
    margin-bottom: 2rem;
}

.dashboard h2 {
    color: #0d47a1;
    margin-bottom: 2rem;
    text-align: center;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.card:hover {
    transform: translateY(-2px);
}

.card h3 {
    color: #0d47a1;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.card-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1a6fc4;
}

.recent-activities {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.recent-activities h3 {
    color: #0d47a1;
    margin-bottom: 1rem;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.activity-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #0d47a1;
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-time {
    color: #0d47a1;
    font-size: 0.85rem;
    min-width: 80px;
    font-weight: 600;
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 12px;
    text-align: center;
}

.activity-text {
    flex: 1;
    margin: 0 1rem;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.4;
}

.activity-user {
    color: #666;
    font-size: 0.8rem;
    font-style: italic;
    min-width: 100px;
    text-align: left;
}

.no-activities, .error-activities {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

.no-activities p, .error-activities p {
    margin: 0;
    font-size: 0.9rem;
}

/* Form styles */
.form-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-container h2 {
    color: #0d47a1;
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    font-family: inherit;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Button styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-left: 0.5rem;
}

.btn-primary {
    background-color: #0d47a1;
    color: white;
}

.btn-primary:hover {
    background-color: #1a6fc4;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Table styles */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    padding: 1.5rem;
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid #e0e0e0;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #0d47a1;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Enhanced Tooth diagram styles */
.tooth-diagram-container {
    border: 3px solid #0d47a1;
    border-radius: 20px;
    padding: 2.5rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #d1e7dd 100%);
    margin: 2rem 0;
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.15);
    position: relative;
    overflow: hidden;
}

.tooth-diagram-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0d47a1, #1565c0, #1976d2, #1565c0, #0d47a1);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.diagram-header {
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #0d47a1, #1565c0);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(13, 71, 161, 0.3);
    position: relative;
    overflow: hidden;
}

.diagram-header::before {
    content: '🦷';
    position: absolute;
    top: -20px;
    right: -20px;
    font-size: 8rem;
    opacity: 0.1;
    transform: rotate(15deg);
}

.diagram-header h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.diagram-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.jaw {
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    border: 2px solid #e3f2fd;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 6px 20px rgba(13, 71, 161, 0.1);
    position: relative;
    transition: all 0.3s ease;
}

.jaw:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.15);
    border-color: #bbdefb;
}

.jaw:last-child {
    margin-bottom: 0;
}

.jaw h4 {
    text-align: center;
    color: #0d47a1;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 1rem;
}

.jaw h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #0d47a1, #1565c0);
    border-radius: 2px;
}

/* Jaw container layout */
.jaw-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,255,0.9));
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(13, 71, 161, 0.1);
    border: 2px solid #e3f2fd;
}

/* Jaw sides */
.jaw-side {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.side-label {
    font-weight: 700;
    color: #0d47a1;
    font-size: 1rem;
    text-align: center;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 12px;
    border: 2px solid #90caf9;
    box-shadow: 0 2px 8px rgba(13, 71, 161, 0.2);
    min-width: 120px;
}

/* Teeth row in each side */
.teeth-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.4rem;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,249,255,0.8));
    border-radius: 15px;
    box-shadow: inset 0 2px 8px rgba(13, 71, 161, 0.1);
    border: 1px solid #e3f2fd;
    min-height: 100px;
    min-width: 0;
}

/* Separator between sides */
.jaw-separator {
    font-size: 3rem;
    font-weight: bold;
    color: #0d47a1;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
    text-shadow: 0 2px 4px rgba(13, 71, 161, 0.3);
    margin: 0 1rem;
}

/* Different colors for upper and lower jaws */
.upper-jaw .jaw-container {
    border-color: #e3f2fd;
}

.upper-jaw .side-label {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #90caf9;
    color: #0d47a1;
}

.lower-jaw .jaw-container {
    border-color: #e8f5e8;
}

.lower-jaw .side-label {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c8);
    border-color: #81c784;
    color: #2e7d32;
}

/* Add separator between jaws */
.jaw.upper-jaw {
    margin-bottom: 3rem;
    position: relative;
}

.jaw.upper-jaw::after {
    content: '';
    position: absolute;
    bottom: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    background: linear-gradient(90deg, transparent, #0d47a1, transparent);
    border-radius: 2px;
}

.tooth {
    width: 45px;
    height: 60px;
    border: 3px solid #e0e0e0;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 50%, #f0f8ff 100%);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    user-select: none;
    position: relative;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin: 0;
    flex-shrink: 0;
    flex-grow: 0;
}

.tooth::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #0d47a1, #1565c0, #1976d2, #1565c0, #0d47a1);
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tooth-icon {
    font-size: 2rem;
    margin-bottom: 4px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    transition: all 0.3s ease;
}

.tooth-number {
    font-size: 0.85rem;
    font-weight: 900;
    color: #0d47a1;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 8px;
    border: 1px solid #e3f2fd;
    min-width: 24px;
    text-align: center;
}

.tooth:hover {
    border-color: #1565c0;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
    transform: translateY(-4px) scale(1.08);
    box-shadow: 0 8px 25px rgba(21, 101, 192, 0.4);
}

.tooth:hover::before {
    opacity: 1;
}

.tooth.selected {
    background: linear-gradient(135deg, #0d47a1 0%, #1565c0 50%, #1976d2 100%);
    color: white;
    border-color: #0d47a1;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 20px rgba(13, 71, 161, 0.5);
    animation: pulse 2s ease-in-out infinite;
}

.tooth.selected::before {
    opacity: 1;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 8px 20px rgba(13, 71, 161, 0.5);
    }
    50% {
        box-shadow: 0 12px 30px rgba(13, 71, 161, 0.7);
    }
}

.tooth.selected .tooth-number {
    color: white;
}

.tooth.selected .tooth-icon {
    filter: brightness(0) invert(1);
}

/* Tooth type specific styles */
.tooth.incisor {
    border-color: #28a745;
}

.tooth.canine {
    border-color: #ffc107;
}

.tooth.premolar {
    border-color: #17a2b8;
}

.tooth.molar {
    border-color: #dc3545;
}

/* Selection summary styles */
.selection-summary {
    margin-top: 3rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    border: 2px solid #e3f2fd;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.1);
    position: relative;
}

.selection-summary::before {
    content: '📋';
    position: absolute;
    top: -15px;
    right: 20px;
    font-size: 2rem;
    background: white;
    padding: 0.5rem;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-card h4 {
    color: #0d47a1;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.selected-display {
    min-height: 60px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.selected-display.empty {
    justify-content: center;
    color: #6c757d;
    font-style: italic;
}

.selected-display.filled {
    border-color: #0d47a1;
    background-color: #e3f2fd;
}

.selected-tooth {
    background: linear-gradient(135deg, #0d47a1, #1565c0);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(13, 71, 161, 0.3);
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.selected-tooth:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 71, 161, 0.4);
}

.remove-tooth {
    background: rgba(255,255,255,0.3);
    border: none;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    line-height: 1;
}

.remove-tooth:hover {
    background: rgba(255,255,255,0.5);
}

.selection-count {
    font-weight: 600;
    color: #0d47a1;
    margin-bottom: 1rem;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9ff, #e8f4fd);
    border-radius: 15px;
    border: 1px solid #e3f2fd;
}

.quick-actions .btn {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 2px solid transparent;
}

.quick-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.quick-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border-color: #6c757d;
}

.quick-actions .btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
}

.quick-actions .btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
}

.quick-actions .btn-success {
    background: linear-gradient(135deg, #28a745, #218838);
    border-color: #28a745;
    font-weight: 700;
    position: relative;
}

.quick-actions .btn-success::before {
    content: '🦷🦷';
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 0.7rem;
    opacity: 0.8;
}

/* Responsive design for tooth diagram */
@media (max-width: 768px) {
    .tooth-diagram-container {
        padding: 1.5rem;
    }

    .jaw-container {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1rem;
    }

    .jaw-separator {
        display: none;
    }

    .side-label {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
        min-width: 100px;
    }

    .teeth-row {
        gap: 0.3rem;
        padding: 0.75rem;
        flex-wrap: nowrap;
        justify-content: flex-start;
        overflow-x: auto;
    }

    .tooth {
        width: 42px;
        height: 52px;
        margin: 0.15rem;
    }

    .tooth-icon {
        font-size: 1.4rem;
    }

    .tooth-number {
        font-size: 0.75rem;
        padding: 1px 4px;
    }

    .quick-actions {
        justify-content: stretch;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .quick-actions .btn {
        flex: 1;
        min-width: 120px;
        font-size: 0.8rem;
        padding: 0.6rem 0.5rem;
    }

    .selected-tooth {
        font-size: 0.8rem;
        padding: 0.3rem 0.5rem;
    }
}

@media (max-width: 480px) {
    .tooth-diagram-container {
        padding: 1rem;
    }

    .jaw-container {
        padding: 0.75rem;
        gap: 1rem;
    }

    .side-label {
        font-size: 0.85rem;
        padding: 0.3rem 0.6rem;
        min-width: 80px;
    }

    .teeth-row {
        gap: 0.2rem;
        padding: 0.5rem;
        overflow-x: auto;
        justify-content: flex-start;
        min-width: 100%;
        flex-wrap: nowrap;
    }

    .tooth {
        width: 38px;
        height: 48px;
        margin: 0.1rem;
        flex-shrink: 0;
    }

    .tooth-icon {
        font-size: 1.1rem;
    }

    .tooth-number {
        font-size: 0.65rem;
        padding: 1px 3px;
    }

    .diagram-header {
        padding: 1.5rem;
    }

    .diagram-header h3 {
        font-size: 1.3rem;
    }

    .jaw h4 {
        font-size: 1.1rem;
    }

    .quick-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .quick-actions .btn {
        width: 100%;
        font-size: 0.85rem;
        padding: 0.75rem;
        min-height: 45px;
    }

    .quick-actions .btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        font-weight: 800;
        border: 2px solid #28a745;
    }
}

/* Responsive design for tabs and lists */
@media (max-width: 768px) {
    .module-header {
        padding: 1.5rem 1rem;
    }

    .module-header h2 {
        font-size: 1.5rem;
    }

    .module-header p {
        font-size: 1rem;
    }

    .tab-navigation {
        flex-direction: column;
    }

    .tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .tab-pane {
        padding: 1rem;
    }

    .list-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .list-actions {
        justify-content: center;
    }

    .search-filter-container {
        flex-direction: column;
        padding: 1rem;
    }

    .search-box {
        min-width: auto;
    }

    .filter-box {
        min-width: auto;
        flex-direction: column;
    }

    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .module-header {
        padding: 1rem;
    }

    .module-header h2 {
        font-size: 1.3rem;
    }

    .tab-btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }

    .tab-pane {
        padding: 0.75rem;
    }

    .list-actions .btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }

    .stats-container {
        grid-template-columns: 1fr 1fr;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }
}

/* Button size variants */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    min-height: 36px;
    line-height: 1.2;
}

/* Module container styles */
.module-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    margin: 1rem 0;
}

.module-header {
    background: linear-gradient(135deg, #0d47a1 0%, #1565c0 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.module-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 600;
}

.module-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

/* Tab navigation styles */
.tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.tab-btn {
    flex: 1;
    padding: 1rem 2rem;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #0d47a1;
}

.tab-btn.active {
    background: white;
    color: #0d47a1;
    border-bottom-color: #0d47a1;
}

/* Tab content styles */
.tab-content {
    min-height: 600px;
}

.tab-pane {
    display: none;
    padding: 2rem;
}

.tab-pane.active {
    display: block;
}

/* List container styles */
.list-container {
    max-width: 100%;
}

.list-header {
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 2px solid #e3f2fd;
}

.list-header.enhanced {
    background: linear-gradient(135deg, #0d47a1 0%, #1565c0 50%, #1976d2 100%);
    color: white;
    position: relative;
}

.list-header.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    animation: rainbow 3s ease-in-out infinite;
}

@keyframes rainbow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    position: relative;
    z-index: 1;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-title h3 {
    margin: 0 0 0.5rem 0;
    color: white;
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-subtitle {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    font-weight: 400;
    opacity: 0.9;
}

.list-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn.enhanced {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn.enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.btn.enhanced .icon {
    font-size: 1.1rem;
}

.btn.enhanced span {
    font-size: 0.9rem;
}

/* Enhanced Search and filter styles */
.search-filter-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: center;
    box-shadow: 0 4px 15px rgba(13, 71, 161, 0.1);
    border: 2px solid #e3f2fd;
    position: relative;
}

.search-filter-container::before {
    content: '🔍';
    position: absolute;
    top: -15px;
    right: 20px;
    font-size: 2rem;
    background: white;
    padding: 0.5rem;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-box {
    flex: 2;
    min-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 3px solid #e3f2fd;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    box-shadow: inset 0 2px 4px rgba(13, 71, 161, 0.1);
}

.search-box input:focus {
    outline: none;
    border-color: #1565c0;
    box-shadow: 0 0 0 4px rgba(21, 101, 192, 0.2);
    background: white;
    transform: translateY(-1px);
}

.search-box input::placeholder {
    color: #6c757d;
    font-style: italic;
}

.filter-box {
    flex: 1;
    display: flex;
    gap: 0.5rem;
    min-width: 200px;
}

.filter-box select {
    flex: 1;
    padding: 1rem;
    border: 3px solid #e3f2fd;
    border-radius: 12px;
    font-size: 0.9rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(13, 71, 161, 0.1);
    font-weight: 500;
}

.filter-box select:focus {
    outline: none;
    border-color: #1565c0;
    box-shadow: 0 0 0 4px rgba(21, 101, 192, 0.2);
    background: white;
    transform: translateY(-1px);
}

.filter-box select:hover {
    border-color: #90caf9;
    background: white;
}

/* Statistics cards styles */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    border-radius: 15px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e3f2fd;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
    transition: height 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
    height: 6px;
}

.stat-icon {
    font-size: 2.5rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
    color: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--card-color);
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-number {
    transform: scale(1.05);
}

.stat-label {
    color: #495057;
    font-size: 1rem;
    margin-top: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Status-specific colors */
.stat-card.pending {
    --card-color: #ffc107;
    --card-color-light: #fff3cd;
}

.stat-card.in-progress {
    --card-color: #17a2b8;
    --card-color-light: #d1ecf1;
}

.stat-card.completed {
    --card-color: #28a745;
    --card-color-light: #d4edda;
}

.stat-card.delivered {
    --card-color: #6c757d;
    --card-color-light: #e2e3e5;
}

/* Additional hover effects for each status */
.stat-card.pending:hover {
    box-shadow: 0 12px 30px rgba(255, 193, 7, 0.3);
}

.stat-card.in-progress:hover {
    box-shadow: 0 12px 30px rgba(23, 162, 184, 0.3);
}

.stat-card.completed:hover {
    box-shadow: 0 12px 30px rgba(40, 167, 69, 0.3);
}

.stat-card.delivered:hover {
    box-shadow: 0 12px 30px rgba(108, 117, 125, 0.3);
}

/* Enhanced table styles */
.table-container {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.15);
}

/* Prostheses table specific styles */
.prostheses-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    background: white;
    font-family: 'Cairo', sans-serif;
}

.prostheses-table th {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    border-bottom: 2px solid #34495e;
    font-size: 13px;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.prostheses-table td {
    padding: 8px 6px;
    text-align: center;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: middle;
    font-size: 12px;
    line-height: 1.4;
}

.prostheses-table tr:hover {
    background-color: #e3f2fd !important;
    transform: scale(1.005);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.prostheses-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.prostheses-table tr:nth-child(odd) {
    background-color: white;
}

/* Action buttons styling */
.action-buttons {
    display: flex;
    gap: 3px;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.action-buttons .btn {
    font-size: 10px;
    padding: 3px 6px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Status select styling */
.status-select {
    font-size: 11px;
    padding: 4px;
    width: 100%;
    border-radius: 4px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.status-select:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.status-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

/* Teeth count badge */
.teeth-count {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
    min-width: 24px;
    text-align: center;
}

/* Responsive table */
@media (max-width: 1200px) {
    .prostheses-table {
        font-size: 11px;
    }

    .prostheses-table th,
    .prostheses-table td {
        padding: 6px 4px;
    }

    .action-buttons {
        flex-direction: column !important;
        gap: 2px !important;
    }

    .action-buttons .btn {
        font-size: 9px !important;
        padding: 2px 4px !important;
        width: 100%;
    }

    .status-select {
        font-size: 10px !important;
    }
}

@media (max-width: 768px) {
    .prostheses-table th,
    .prostheses-table td {
        padding: 4px 2px;
        font-size: 10px;
    }

    .action-buttons .btn {
        font-size: 8px !important;
        padding: 1px 3px !important;
    }
}
    border: 2px solid #e3f2fd;
    position: relative;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0d47a1, #1565c0, #1976d2, #1565c0, #0d47a1);
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th {
    background: linear-gradient(135deg, #0d47a1 0%, #1565c0 50%, #1976d2 100%);
    color: white;
    padding: 1.5rem 1rem;
    text-align: center;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    border-bottom: 3px solid rgba(255, 255, 255, 0.2);
}

.table th:hover {
    background: linear-gradient(135deg, #0c3d91 0%, #1357b0 50%, #1565c0 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(13, 71, 161, 0.3);
}

.table th:first-child {
    border-top-right-radius: 20px;
}

.table th:last-child {
    border-top-left-radius: 20px;
}

.table td {
    padding: 1.25rem 1rem;
    text-align: center;
    border-bottom: 1px solid #e3f2fd;
    vertical-align: middle;
    font-weight: 500;
    transition: all 0.3s ease;
}

.table tr:nth-child(even) {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.table tr:nth-child(odd) {
    background: white;
}

.table tr:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 15px rgba(13, 71, 161, 0.1);
}

.table tr:hover td {
    color: #0d47a1;
    font-weight: 600;
}

/* Status select styles */
.status-select {
    padding: 0.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.status-select:focus {
    outline: none;
    border-color: #0d47a1;
    box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.1);
}

.status-select.pending {
    border-color: #ffc107;
    background: #fff3cd;
}

.status-select.in_progress {
    border-color: #17a2b8;
    background: #d1ecf1;
}

.status-select.completed {
    border-color: #28a745;
    background: #d4edda;
}

.status-select.delivered {
    border-color: #6c757d;
    background: #e2e3e5;
}

/* Action buttons styles */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    min-width: 60px;
}

/* Teeth count badge */
.teeth-count {
    background: #0d47a1;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 50%;
    font-weight: bold;
    font-size: 0.9rem;
    min-width: 30px;
    display: inline-block;
}

/* Status badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.in-progress {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.delivered {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.btn-xs {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    min-height: 28px;
    line-height: 1.2;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    min-height: 50px;
}

/* Reports styles */
.reports-container {
    margin-bottom: 2rem;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.report-header h3 {
    color: #0d47a1;
    margin: 0;
}

.financial-summary,
.prostheses-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.summary-item {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-item .label {
    font-weight: 600;
    color: #333;
}

.summary-item .value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #0d47a1;
}

.summary-item .value.profit {
    color: #28a745;
}

.summary-item .value.loss {
    color: #dc3545;
}

.report-section {
    margin-bottom: 2rem;
}

.report-section h4 {
    color: #0d47a1;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

/* Financial dashboard styles */
.financial-dashboard {
    margin-bottom: 2rem;
}

.financial-dashboard h2 {
    color: #0d47a1;
    text-align: center;
    margin-bottom: 2rem;
}

/* Alert styles */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Status select styling */
.status-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: white;
    cursor: pointer;
}

.status-select:focus {
    outline: none;
    border-color: #0d47a1;
    box-shadow: 0 0 0 2px rgba(13, 71, 161, 0.2);
}

/* Optgroup styling */
optgroup {
    font-weight: bold;
    color: #0d47a1;
    background-color: #f8f9fa;
    font-style: normal;
}

optgroup option {
    font-weight: normal;
    color: #333;
    background-color: white;
    padding-right: 20px;
}

/* Enhanced select styling */
select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 16px;
    padding-left: 40px;
}

/* Search input styling */
.search-input {
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    padding: 0.5rem;
    font-size: 0.9rem;
    transition: border-color 0.3s;
}

.search-input:focus {
    outline: none;
    border-color: #0d47a1;
    box-shadow: 0 0 0 2px rgba(13, 71, 161, 0.2);
}

.search-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Auto-generated case ID styling */
input[readonly] {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
    display: block;
}

.text-muted {
    color: #6c757d !important;
}

/* Status badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 60px;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* User management specific styles */
.users-container {
    margin-bottom: 2rem;
}

.user-info-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.user-info-card h4 {
    color: #0d47a1;
    margin-bottom: 0.5rem;
}

.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.user-stat-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.user-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d47a1;
}

.user-stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

/* User welcome styles */
.user-welcome {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-width: 200px;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    font-size: 1rem;
}

.user-role {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 0.25rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Lab settings styles */
.lab-info-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.lab-info-card h4 {
    color: #0d47a1;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0.5rem;
}

.info-item {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.info-item strong {
    min-width: 120px;
    color: #333;
    font-weight: 600;
}

.info-item a {
    color: #0d47a1;
    text-decoration: none;
}

.info-item a:hover {
    text-decoration: underline;
}

.phone-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    align-items: center;
}

.phone-input-group input {
    flex: 1;
}

.phone-input-group button {
    flex-shrink: 0;
}

/* Phones section styles */
.phones-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 2rem;
    margin: 1rem 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.phones-section .form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.phones-section .form-group:last-child {
    margin-bottom: 0;
}

.phones-section label {
    font-weight: 700;
    color: #0d47a1;
    margin-bottom: 0.75rem;
    display: block;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.phones-section input[type="tel"] {
    width: 100%;
    padding: 1.25rem 1.5rem;
    border: 3px solid #ddd;
    border-radius: 10px;
    font-size: 1.2rem;
    direction: ltr;
    text-align: center;
    background: white;
    transition: all 0.3s ease;
    min-height: 60px;
    font-weight: 600;
    letter-spacing: 1px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.phones-section input[type="tel"]:focus {
    border-color: #0d47a1;
    outline: none;
    box-shadow: 0 0 0 4px rgba(13, 71, 161, 0.15);
    background: #f8f9ff;
    transform: translateY(-1px);
}

.phones-section input[type="tel"]:hover {
    border-color: #1976d2;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.phones-section input[type="tel"]::placeholder {
    color: #999;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* Logo upload section improvements */
.logo-upload-section {
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    background: #fafafa;
    transition: border-color 0.3s;
}

.logo-upload-section input[type="file"] {
    padding: 0.75rem;
    font-size: 1rem;
    border: 2px solid #ddd;
    border-radius: 6px;
    background: white;
    width: 100%;
    max-width: 300px;
}

/* Logo preview button improvements */
.logo-preview .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    margin-top: 0.75rem;
    min-height: 36px;
}

/* Form button spacing */
.form-group .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.form-group .btn:last-child {
    margin-right: 0;
}

/* Logo styles */
.login-logo {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem;
}

.login-logo-img {
    max-width: 200px;
    max-height: 100px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.main-logo {
    display: flex;
    align-items: center;
    margin-left: 1rem;
}

.main-logo-img {
    max-width: 120px;
    max-height: 60px;
    border-radius: 4px;
}

.logo-upload-section {
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    background: #fafafa;
    transition: border-color 0.3s;
}

.logo-upload-section:hover {
    border-color: #0d47a1;
}

.logo-preview {
    margin-top: 1rem;
    text-align: center;
}

.logo-preview img {
    display: block;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.no-logo-placeholder {
    background: #f5f5f5;
    color: #999;
    font-style: italic;
}

/* Header logo integration */
header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #0d47a1, #1976d2);
    color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 1rem;
}

.header-title {
    flex: 1;
    text-align: center;
}

.header-title h1 {
    margin: 0;
    color: white;
    font-size: 1.8rem;
}

#user-info {
    color: white;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Login container logo positioning */
.login-container {
    position: relative;
}

/* Invoice logo styles */
.invoice-logo img {
    filter: brightness(1.1);
    border-radius: 8px;
}

/* Responsive logo styles */
@media (max-width: 768px) {
    .main-logo-img {
        max-width: 80px;
        max-height: 40px;
    }

    .login-logo-img {
        max-width: 150px;
        max-height: 75px;
    }

    .header-title h1 {
        font-size: 1.4rem;
    }
}

/* Logo animation */
.login-logo-img, .main-logo-img {
    transition: transform 0.3s ease;
}

.login-logo-img:hover, .main-logo-img:hover {
    transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav li {
        margin: 0.5rem 0;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .activity-time {
        margin-bottom: 0.5rem;
    }
}

/* Main Navigation styles */
#main-nav {
    background: linear-gradient(135deg, #0d47a1, #1565c0);
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: none;
}

#main-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 2rem;
}

#main-nav li {
    margin: 0;
}

#main-nav a {
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#main-nav a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Dropdown menu styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 2px solid #0d47a1;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.2);
    min-width: 280px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    list-style: none;
    margin: 0;
    padding: 0.75rem 0;
    display: block !important;
}

.dropdown-menu.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

.dropdown-menu li {
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #212529;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
}

.dropdown-menu a:hover {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #0d47a1;
    padding-right: 2rem;
    transform: none;
    font-weight: 700;
}

/* Logout button special styling */
.logout-btn {
    background: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
    border-radius: 6px !important;
}

.logout-btn:hover {
    background: #dc3545 !important;
    color: white !important;
}

/* Responsive navigation */
@media (max-width: 768px) {
    #main-nav ul {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    #main-nav a {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        border: none;
        background: rgba(255, 255, 255, 0.1);
        min-width: auto;
        width: 100%;
        margin-top: 0.5rem;
    }

    .dropdown-menu a {
        color: white;
        padding: 0.5rem 1rem;
    }

    .dropdown-menu a:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding-right: 1rem;
    }
}

/* Enhanced dropdown menu visibility */
.dropdown-menu a {
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.dropdown-menu a:hover {
    border-left-color: #0d47a1;
    box-shadow: inset 0 0 10px rgba(13, 71, 161, 0.1);
}

/* Improved text contrast for dropdown items */
.dropdown-menu a {
    color: #1a1a1a !important;
    font-weight: 700 !important;
    text-decoration: none !important;
}

.dropdown-menu a:hover {
    color: #0d47a1 !important;
    background: linear-gradient(135deg, #e8f4fd, #d1e7dd) !important;
}

/* Add icons spacing in dropdown */
.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Ensure dropdown is always visible when shown */
.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    z-index: 9999 !important;
}

/* Modal styles for doctor price list */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #0d47a1, #1565c0);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

.price-list-container {
    display: grid;
    gap: 20px;
}

.price-category {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.price-category:hover {
    border-color: #0d47a1;
    box-shadow: 0 4px 15px rgba(13, 71, 161, 0.1);
}

.category-title {
    color: #0d47a1;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
    text-align: center;
}

.price-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.price-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.price-item:hover {
    border-color: #0d47a1;
    box-shadow: 0 2px 8px rgba(13, 71, 161, 0.1);
}

.price-item-label {
    flex: 1;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.price-item-input {
    width: 120px;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.price-item-input:focus {
    outline: none;
    border-color: #0d47a1;
    box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.1);
}

.price-item-currency {
    color: #666;
    font-weight: 600;
    font-size: 0.9rem;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
    border: none;
}

.btn-info:hover {
    background-color: #138496;
}

/* Responsive modal */
@media (max-width: 768px) {
    .modal {
        padding: 10px;
    }

    .modal-content {
        max-height: 95vh;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .modal-body {
        padding: 20px;
    }

    .price-items {
        grid-template-columns: 1fr;
    }

    .price-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .price-item-input {
        width: 100%;
        max-width: 200px;
    }

    /* Quick Actions Responsive */
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .quick-action-btn {
        padding: 12px 15px;
    }

    .quick-action-icon {
        font-size: 2em;
        margin-left: 10px;
    }

    .quick-action-text h4 {
        font-size: 1em;
    }

    .quick-action-text p {
        font-size: 0.85em;
    }

    /* General responsive */
    .form-row {
        grid-template-columns: 1fr;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav li {
        margin: 0.25rem 0;
    }

    .table-container {
        overflow-x: auto;
    }

    .btn {
        margin-bottom: 0.5rem;
        margin-left: 0;
    }
}

/* External Labs Module Styles */
.status-select {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.85rem;
    min-width: 120px;
}

.status-select.pending {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.status-select.in_progress {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.status-select.completed {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.status-select.delivered {
    background-color: #e2e3e5;
    border-color: #d6d8db;
    color: #383d41;
}

/* Enhanced list header */
.list-header.enhanced {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    margin-bottom: 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-title h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.header-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

.list-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.btn.enhanced {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn.enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn.enhanced .icon {
    font-size: 1.1em;
}

.btn-primary.enhanced {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-info.enhanced {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
}

/* Tab navigation improvements */
.tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    padding: 0.5rem;
    gap: 0.25rem;
    margin-bottom: 0;
}

.tab-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: #6c757d;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-align: center;
}

.tab-btn:hover {
    background: rgba(13, 71, 161, 0.1);
    color: #0d47a1;
}

.tab-btn.active {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(13, 71, 161, 0.3);
}

/* Form enhancements for external labs */
.form-container h3 {
    color: #0d47a1;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3f2fd;
}

/* Table improvements */
.table-container {
    background: white;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    text-align: center;
    padding: 1rem 0.75rem;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Responsive improvements for external labs */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .list-actions {
        justify-content: center;
        width: 100%;
    }

    .tab-navigation {
        flex-direction: column;
    }

    .tab-btn {
        margin-bottom: 0.25rem;
    }

    .status-select {
        min-width: 100px;
        font-size: 0.8rem;
    }
}

/* Button arrangement improvements */
.form-group .btn {
    margin-left: 10px;
}

.form-group .btn:first-child {
    margin-left: 0;
}

/* Cancel button specific styling */
#cancel-doctor-edit-btn,
#cancel-employee-edit-btn,
#cancel-user-edit-btn,
#cancel-expense-edit-btn,
#cancel-prosthesis-edit-btn {
    margin-left: 10px;
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

#cancel-doctor-edit-btn:hover,
#cancel-employee-edit-btn:hover,
#cancel-user-edit-btn:hover,
#cancel-expense-edit-btn:hover,
#cancel-prosthesis-edit-btn:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Ensure buttons are properly aligned */
.form-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

.form-group .btn {
    margin: 0;
}

/* For mobile responsiveness */
@media (max-width: 576px) {
    .form-group {
        flex-direction: column;
        align-items: stretch;
    }

    .form-group .btn {
        margin-bottom: 10px;
        width: 100%;
    }

    .form-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Doctors table specific styling */
.prostheses-count {
    display: inline-block;
    min-width: 30px;
    text-align: center;
    font-family: 'Arial', sans-serif;
}

/* Action buttons styling */
.action-buttons .btn {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.action-buttons .btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

.action-buttons .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.action-buttons .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
    border: none;
}

/* Module container improvements */
.module-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.module-header {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.module-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.module-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

/* Tab content styling */
.tab-content {
    background: #f8f9fa;
}

.tab-pane {
    display: none;
    padding: 2rem;
}

.tab-pane.active {
    display: block;
}

/* Enhanced form styling for doctors */
.form-container h3 {
    color: #0d47a1;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3f2fd;
    font-size: 1.3rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 8px !important;
    }

    .action-buttons .btn {
        width: 100%;
        margin: 0;
    }
}

/* Modal Styles for Doctor Statement */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
}

.modal-body .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.modal-body .form-group {
    margin-bottom: 1rem;
}

.modal-body .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.modal-body .form-group input[type="date"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.modal-body .form-group input[type="date"]:focus {
    outline: none;
    border-color: #0d47a1;
    box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.1);
}

.modal-body .form-group input[type="checkbox"] {
    margin-left: 0.5rem;
    transform: scale(1.2);
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.modal-footer .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.modal-footer .btn-primary {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    border: none;
    color: white;
}

.modal-footer .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3);
}

.modal-footer .btn-secondary {
    background: #6c757d;
    border: none;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Responsive modal */
@media (max-width: 576px) {
    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-body .form-row {
        grid-template-columns: 1fr;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* ===============================
   DISPLAY SETTINGS STYLES
   =============================== */

/* Table density variations */
.table-compact {
    font-size: 0.85rem;
}

.table-compact th,
.table-compact td {
    padding: 0.3rem 0.5rem;
    line-height: 1.2;
}

.table-comfortable {
    font-size: 1.1rem;
}

.table-comfortable th,
.table-comfortable td {
    padding: 1rem 0.75rem;
    line-height: 1.6;
}

/* Navigation width variations */
.nav-narrow {
    width: 180px;
}

.nav-narrow .nav-item {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
}

.nav-wide {
    width: 280px;
}

.nav-wide .nav-item {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

/* Container width variations */
.container-narrow {
    max-width: 1200px;
}

.container-wide {
    max-width: 1600px;
}

.container-full {
    max-width: 100%;
    width: 100%;
}

/* Display preview styles */
.display-preview {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background-color: #f8f9fa;
    margin-top: 1rem;
}

.preview-content {
    background-color: white;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.preview-table {
    margin: 1rem 0;
    font-size: inherit;
}

.preview-btn {
    margin-top: 0.5rem;
}

/* Screen size indicators */
.screen-info {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 0.75rem;
    margin: 1rem 0;
    font-size: 0.9rem;
}

/* Responsive adjustments for display settings */
@media (max-width: 1366px) {
    body.auto-adjust {
        zoom: 0.9;
        font-size: 13px;
    }
}

@media (min-width: 1920px) {
    body.auto-adjust {
        zoom: 1.1;
        font-size: 15px;
    }
}

/* High DPI screen adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* ===============================
   FINANCIAL TYPE STYLES
   =============================== */

/* Financial type indicators */
.financial-type {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
    text-align: center;
    min-width: 80px;
}

.financial-type.expense-type {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.financial-type.income-type {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Form text styling for financial type help */
.form-text {
    font-style: italic;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}