/**
 * مدير الأجهزة المتقدم
 * Advanced Device Manager
 * 
 * يدير التفاعل مع الأجهزة المختلفة ويحسن الأداء حسب نوع الجهاز
 */

class AdvancedDeviceManager {
    constructor() {
        this.device = this.detectDevice();
        this.touchSupport = this.detectTouchSupport();
        this.screenSize = this.getScreenSize();
        this.orientation = this.getOrientation();
        this.performance = this.getPerformanceLevel();
        this.connection = this.getConnectionInfo();
        
        this.initializeEventListeners();
        this.optimizeForDevice();
        this.setupGestures();
    }

    /**
     * كشف نوع الجهاز
     */
    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const platform = navigator.platform.toLowerCase();
        
        // كشف iOS
        if (/iphone|ipad|ipod/.test(userAgent) || (platform === 'macintel' && navigator.maxTouchPoints > 1)) {
            return {
                type: 'ios',
                name: /ipad/.test(userAgent) ? 'iPad' : 'iPhone',
                version: this.getIOSVersion(userAgent),
                isTablet: /ipad/.test(userAgent) || (platform === 'macintel' && navigator.maxTouchPoints > 1)
            };
        }
        
        // كشف Android
        if (/android/.test(userAgent)) {
            return {
                type: 'android',
                name: 'Android',
                version: this.getAndroidVersion(userAgent),
                isTablet: !/mobile/.test(userAgent)
            };
        }
        
        // كشف Windows Mobile
        if (/windows phone/.test(userAgent)) {
            return {
                type: 'windows',
                name: 'Windows Phone',
                version: this.getWindowsVersion(userAgent),
                isTablet: false
            };
        }
        
        // كشف سطح المكتب
        return {
            type: 'desktop',
            name: this.getDesktopOS(),
            version: null,
            isTablet: false
        };
    }

    /**
     * كشف دعم اللمس
     */
    detectTouchSupport() {
        return {
            supported: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
            maxTouchPoints: navigator.maxTouchPoints || 0,
            multiTouch: navigator.maxTouchPoints > 1
        };
    }

    /**
     * الحصول على حجم الشاشة
     */
    getScreenSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio || 1,
            availableWidth: screen.availWidth,
            availableHeight: screen.availHeight,
            colorDepth: screen.colorDepth
        };
    }

    /**
     * الحصول على اتجاه الشاشة
     */
    getOrientation() {
        const orientation = screen.orientation || screen.mozOrientation || screen.msOrientation;
        return {
            angle: orientation ? orientation.angle : window.orientation || 0,
            type: orientation ? orientation.type : (window.innerWidth > window.innerHeight ? 'landscape' : 'portrait')
        };
    }

    /**
     * تقييم مستوى الأداء
     */
    getPerformanceLevel() {
        const memory = navigator.deviceMemory || 4; // GB
        const cores = navigator.hardwareConcurrency || 4;
        const connection = navigator.connection || {};
        
        let score = 0;
        
        // تقييم الذاكرة
        if (memory >= 8) score += 30;
        else if (memory >= 4) score += 20;
        else if (memory >= 2) score += 10;
        
        // تقييم المعالج
        if (cores >= 8) score += 30;
        else if (cores >= 4) score += 20;
        else if (cores >= 2) score += 10;
        
        // تقييم الاتصال
        if (connection.effectiveType === '4g') score += 20;
        else if (connection.effectiveType === '3g') score += 10;
        else if (connection.effectiveType === '2g') score += 5;
        
        // تقييم نوع الجهاز
        if (this.device.type === 'desktop') score += 20;
        else if (this.device.isTablet) score += 10;
        
        if (score >= 80) return 'high';
        if (score >= 50) return 'medium';
        return 'low';
    }

    /**
     * معلومات الاتصال
     */
    getConnectionInfo() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        
        if (!connection) {
            return {
                type: 'unknown',
                effectiveType: 'unknown',
                downlink: null,
                rtt: null,
                saveData: false
            };
        }
        
        return {
            type: connection.type || 'unknown',
            effectiveType: connection.effectiveType || 'unknown',
            downlink: connection.downlink || null,
            rtt: connection.rtt || null,
            saveData: connection.saveData || false
        };
    }

    /**
     * تهيئة مستمعي الأحداث
     */
    initializeEventListeners() {
        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', this.debounce(() => {
            this.screenSize = this.getScreenSize();
            this.handleScreenSizeChange();
        }, 250));

        // مراقبة تغيير الاتجاه
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.orientation = this.getOrientation();
                this.handleOrientationChange();
            }, 100);
        });

        // مراقبة تغيير الاتصال
        if (navigator.connection) {
            navigator.connection.addEventListener('change', () => {
                this.connection = this.getConnectionInfo();
                this.handleConnectionChange();
            });
        }

        // مراقبة حالة البطارية (إذا كانت متاحة)
        if (navigator.getBattery) {
            navigator.getBattery().then(battery => {
                this.battery = {
                    level: battery.level,
                    charging: battery.charging,
                    chargingTime: battery.chargingTime,
                    dischargingTime: battery.dischargingTime
                };
                
                battery.addEventListener('levelchange', () => {
                    this.battery.level = battery.level;
                    this.handleBatteryChange();
                });
                
                battery.addEventListener('chargingchange', () => {
                    this.battery.charging = battery.charging;
                    this.handleBatteryChange();
                });
            });
        }
    }

    /**
     * تحسين النظام حسب الجهاز
     */
    optimizeForDevice() {
        const body = document.body;
        
        // إضافة فئات CSS حسب الجهاز
        body.classList.add(`device-${this.device.type}`);
        body.classList.add(`performance-${this.performance}`);
        
        if (this.device.isTablet) {
            body.classList.add('device-tablet');
        }
        
        if (this.touchSupport.supported) {
            body.classList.add('touch-enabled');
        }
        
        if (this.touchSupport.multiTouch) {
            body.classList.add('multitouch-enabled');
        }
        
        // تحسين الأداء حسب مستوى الجهاز
        this.applyPerformanceOptimizations();
        
        // تحسين الواجهة حسب حجم الشاشة
        this.applyScreenOptimizations();
    }

    /**
     * تطبيق تحسينات الأداء
     */
    applyPerformanceOptimizations() {
        const root = document.documentElement;
        
        switch (this.performance) {
            case 'low':
                // تقليل الرسوم المتحركة
                root.style.setProperty('--transition-fast', '0.1s ease');
                root.style.setProperty('--transition-normal', '0.2s ease');
                root.style.setProperty('--transition-slow', '0.3s ease');
                
                // تقليل الظلال
                root.style.setProperty('--shadow-lg', '0 4px 6px rgba(0, 0, 0, 0.1)');
                root.style.setProperty('--shadow-xl', '0 8px 15px rgba(0, 0, 0, 0.1)');
                break;
                
            case 'medium':
                // تحسينات متوسطة
                root.style.setProperty('--transition-fast', '0.15s ease');
                root.style.setProperty('--transition-normal', '0.25s ease');
                root.style.setProperty('--transition-slow', '0.4s ease');
                break;
                
            case 'high':
                // تفعيل جميع التأثيرات
                root.style.setProperty('--transition-fast', '0.2s cubic-bezier(0.4, 0, 0.2, 1)');
                root.style.setProperty('--transition-normal', '0.3s cubic-bezier(0.4, 0, 0.2, 1)');
                root.style.setProperty('--transition-slow', '0.5s cubic-bezier(0.4, 0, 0.2, 1)');
                break;
        }
        
        // تحسين الاتصال البطيء
        if (this.connection.saveData || this.connection.effectiveType === '2g') {
            this.enableDataSavingMode();
        }
    }

    /**
     * تطبيق تحسينات الشاشة
     */
    applyScreenOptimizations() {
        const root = document.documentElement;
        
        // تحديد نقاط الكسر
        if (this.screenSize.width < 480) {
            root.classList.add('screen-xs');
        } else if (this.screenSize.width < 768) {
            root.classList.add('screen-sm');
        } else if (this.screenSize.width < 1024) {
            root.classList.add('screen-md');
        } else if (this.screenSize.width < 1280) {
            root.classList.add('screen-lg');
        } else {
            root.classList.add('screen-xl');
        }
        
        // تحسين الخط حسب كثافة البكسل
        if (this.screenSize.devicePixelRatio > 2) {
            root.style.setProperty('font-size', '15px');
        } else if (this.screenSize.devicePixelRatio > 1.5) {
            root.style.setProperty('font-size', '16px');
        }
    }

    /**
     * إعداد الإيماءات
     */
    setupGestures() {
        if (!this.touchSupport.supported) return;
        
        this.gestures = {
            swipeThreshold: 50,
            tapThreshold: 10,
            longPressThreshold: 500,
            pinchThreshold: 0.1
        };
        
        this.setupSwipeGestures();
        this.setupPinchGestures();
        this.setupTapGestures();
    }

    /**
     * إعداد إيماءات السحب
     */
    setupSwipeGestures() {
        let startX, startY, startTime;
        
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                startTime = Date.now();
            }
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            if (e.changedTouches.length === 1) {
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const endTime = Date.now();
                
                const deltaX = endX - startX;
                const deltaY = endY - startY;
                const deltaTime = endTime - startTime;
                
                if (deltaTime < 300 && Math.abs(deltaX) > this.gestures.swipeThreshold) {
                    if (Math.abs(deltaX) > Math.abs(deltaY)) {
                        const direction = deltaX > 0 ? 'right' : 'left';
                        this.handleSwipe(direction, e);
                    }
                }
            }
        }, { passive: true });
    }

    /**
     * إعداد إيماءات القرص
     */
    setupPinchGestures() {
        let initialDistance = 0;
        
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length === 2) {
                initialDistance = this.getDistance(e.touches[0], e.touches[1]);
            }
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
            if (e.touches.length === 2) {
                const currentDistance = this.getDistance(e.touches[0], e.touches[1]);
                const scale = currentDistance / initialDistance;
                
                if (Math.abs(scale - 1) > this.gestures.pinchThreshold) {
                    this.handlePinch(scale, e);
                }
            }
        }, { passive: true });
    }

    /**
     * إعداد إيماءات النقر
     */
    setupTapGestures() {
        let tapTimeout;
        let lastTap = 0;
        
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            const timeSinceLastTap = now - lastTap;
            
            if (timeSinceLastTap < 300 && timeSinceLastTap > 0) {
                // نقرة مزدوجة
                clearTimeout(tapTimeout);
                this.handleDoubleTap(e);
            } else {
                // نقرة واحدة
                tapTimeout = setTimeout(() => {
                    this.handleSingleTap(e);
                }, 300);
            }
            
            lastTap = now;
        }, { passive: true });
    }

    /**
     * معالجة السحب
     */
    handleSwipe(direction, event) {
        // فتح/إغلاق الشريط الجانبي بالسحب
        if (direction === 'left' && this.screenSize.width < 768) {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar && sidebar.classList.contains('active')) {
                this.closeSidebar();
            }
        } else if (direction === 'right' && this.screenSize.width < 768) {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar && !sidebar.classList.contains('active')) {
                this.openSidebar();
            }
        }
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('swipe', {
            detail: { direction, originalEvent: event }
        }));
    }

    /**
     * معالجة القرص
     */
    handlePinch(scale, event) {
        // منع التكبير الافتراضي
        event.preventDefault();
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('pinch', {
            detail: { scale, originalEvent: event }
        }));
    }

    /**
     * معالجة النقرة الواحدة
     */
    handleSingleTap(event) {
        // إضافة تأثير النقر
        this.addRippleEffect(event);
    }

    /**
     * معالجة النقرة المزدوجة
     */
    handleDoubleTap(event) {
        // منع التكبير الافتراضي
        event.preventDefault();
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('doubletap', {
            detail: { originalEvent: event }
        }));
    }

    /**
     * إضافة تأثير الموجة عند النقر
     */
    addRippleEffect(event) {
        const target = event.target.closest('.btn, .card, .nav-item');
        if (!target) return;
        
        const rect = target.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.changedTouches[0].clientX - rect.left - size / 2;
        const y = event.changedTouches[0].clientY - rect.top - size / 2;
        
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1000;
        `;
        
        target.style.position = 'relative';
        target.style.overflow = 'hidden';
        target.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * حساب المسافة بين نقطتين
     */
    getDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * تأخير التنفيذ
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * معالجة تغيير حجم الشاشة
     */
    handleScreenSizeChange() {
        this.applyScreenOptimizations();
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('screenSizeChange', {
            detail: { screenSize: this.screenSize }
        }));
    }

    /**
     * معالجة تغيير الاتجاه
     */
    handleOrientationChange() {
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('orientationChange', {
            detail: { orientation: this.orientation }
        }));
    }

    /**
     * معالجة تغيير الاتصال
     */
    handleConnectionChange() {
        if (this.connection.saveData || this.connection.effectiveType === '2g') {
            this.enableDataSavingMode();
        } else {
            this.disableDataSavingMode();
        }
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('connectionChange', {
            detail: { connection: this.connection }
        }));
    }

    /**
     * معالجة تغيير البطارية
     */
    handleBatteryChange() {
        if (this.battery.level < 0.2 && !this.battery.charging) {
            this.enablePowerSavingMode();
        } else {
            this.disablePowerSavingMode();
        }
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('batteryChange', {
            detail: { battery: this.battery }
        }));
    }

    /**
     * تفعيل وضع توفير البيانات
     */
    enableDataSavingMode() {
        document.body.classList.add('data-saving-mode');
        
        // تقليل جودة الصور
        document.querySelectorAll('img').forEach(img => {
            if (img.dataset.lowQuality) {
                img.src = img.dataset.lowQuality;
            }
        });
        
        // تعطيل الرسوم المتحركة غير الضرورية
        document.documentElement.style.setProperty('--transition-normal', '0s');
    }

    /**
     * إلغاء وضع توفير البيانات
     */
    disableDataSavingMode() {
        document.body.classList.remove('data-saving-mode');
        
        // استعادة جودة الصور
        document.querySelectorAll('img').forEach(img => {
            if (img.dataset.highQuality) {
                img.src = img.dataset.highQuality;
            }
        });
        
        // استعادة الرسوم المتحركة
        document.documentElement.style.removeProperty('--transition-normal');
    }

    /**
     * تفعيل وضع توفير الطاقة
     */
    enablePowerSavingMode() {
        document.body.classList.add('power-saving-mode');
        
        // تقليل معدل التحديث
        this.reducedRefreshRate = true;
        
        // تقليل الرسوم المتحركة
        document.documentElement.style.setProperty('--transition-fast', '0.1s ease');
        document.documentElement.style.setProperty('--transition-normal', '0.15s ease');
    }

    /**
     * إلغاء وضع توفير الطاقة
     */
    disablePowerSavingMode() {
        document.body.classList.remove('power-saving-mode');
        
        this.reducedRefreshRate = false;
        
        // استعادة الرسوم المتحركة
        this.applyPerformanceOptimizations();
    }

    /**
     * فتح الشريط الجانبي
     */
    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.add('active');
            if (overlay) overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * إغلاق الشريط الجانبي
     */
    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.remove('active');
            if (overlay) overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    /**
     * الحصول على إصدار iOS
     */
    getIOSVersion(userAgent) {
        const match = userAgent.match(/os (\d+)_(\d+)_?(\d+)?/);
        return match ? `${match[1]}.${match[2]}.${match[3] || 0}` : null;
    }

    /**
     * الحصول على إصدار Android
     */
    getAndroidVersion(userAgent) {
        const match = userAgent.match(/android (\d+(?:\.\d+)*)/);
        return match ? match[1] : null;
    }

    /**
     * الحصول على إصدار Windows
     */
    getWindowsVersion(userAgent) {
        const match = userAgent.match(/windows phone (?:os )?(\d+(?:\.\d+)*)/);
        return match ? match[1] : null;
    }

    /**
     * الحصول على نظام تشغيل سطح المكتب
     */
    getDesktopOS() {
        const platform = navigator.platform.toLowerCase();
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (platform.includes('win')) return 'Windows';
        if (platform.includes('mac')) return 'macOS';
        if (platform.includes('linux')) return 'Linux';
        if (userAgent.includes('cros')) return 'Chrome OS';
        
        return 'Unknown';
    }

    /**
     * الحصول على معلومات الجهاز
     */
    getDeviceInfo() {
        return {
            device: this.device,
            touchSupport: this.touchSupport,
            screenSize: this.screenSize,
            orientation: this.orientation,
            performance: this.performance,
            connection: this.connection,
            battery: this.battery || null
        };
    }
}

// إنشاء مثيل عام من مدير الأجهزة
const deviceManager = new AdvancedDeviceManager();

// إضافة أنماط CSS للرسوم المتحركة
const rippleStyles = document.createElement('style');
rippleStyles.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyles);

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AdvancedDeviceManager, deviceManager };
}
