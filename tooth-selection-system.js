/**
 * نظام اختيار الأسنان - مستخرج من مشروع معمل الأسنان
 * Tooth Selection System - Extracted from Dental Lab Project
 * 
 * يحتوي على:
 * - مخطط الأسنان التفاعلي
 * - نظام ترقيم الأسنان (1-8 لكل جانب)
 * - اختيار وإلغاء اختيار الأسنان
 * - عرض الأسنان المختارة
 * - حفظ بيانات الأسنان
 */

// متغيرات عامة
let selectedTeeth = [];

/**
 * إعداد مخطط الأسنان التفاعلي
 * Setup Interactive Tooth Diagram
 */
function setupToothDiagram() {
    const toothDiagram = document.getElementById('tooth-diagram');
    selectedTeeth = [];

    // إنشاء HTML لمخطط الأسنان
    toothDiagram.innerHTML = `
        <div class="tooth-diagram-container">
            <div class="diagram-header">
                <h3>🦷 مخطط الأسنان التفاعلي</h3>
                <p>انقر على أيقونة السن لاختيارها أو إلغاء اختيارها</p>
            </div>

            <div class="jaw upper-jaw">
                <h4>الفك العلوي (Upper Jaw)</h4>
                <div class="jaw-container">
                    <div class="jaw-side right-side">
                        <div class="side-label">الجانب الأيمن</div>
                        <div class="teeth-row">
                            ${generateTeethHTML(8, 1, 'upper-right')}
                        </div>
                    </div>
                    <div class="jaw-separator">|</div>
                    <div class="jaw-side left-side">
                        <div class="side-label">الجانب الأيسر</div>
                        <div class="teeth-row">
                            ${generateTeethHTML(1, 8, 'upper-left')}
                        </div>
                    </div>
                </div>
            </div>

            <div class="jaw lower-jaw">
                <h4>الفك السفلي (Lower Jaw)</h4>
                <div class="jaw-container">
                    <div class="jaw-side right-side">
                        <div class="side-label">الجانب الأيمن</div>
                        <div class="teeth-row">
                            ${generateTeethHTML(8, 1, 'lower-right')}
                        </div>
                    </div>
                    <div class="jaw-separator">|</div>
                    <div class="jaw-side left-side">
                        <div class="side-label">الجانب الأيسر</div>
                        <div class="teeth-row">
                            ${generateTeethHTML(1, 8, 'lower-left')}
                        </div>
                    </div>
                </div>
            </div>

            <div class="selection-summary">
                <div class="summary-card">
                    <h4>الأسنان المختارة</h4>
                    <div id="selected-teeth-display" class="selected-display">
                        لم يتم اختيار أي أسنان بعد
                    </div>
                    <div class="selection-count">
                        عدد الأسنان: <span id="selection-count">0</span>
                    </div>
                </div>
                <div class="quick-actions">
                    <button type="button" onclick="selectAllTeeth()" class="btn btn-sm btn-secondary">اختيار الكل</button>
                    <button type="button" onclick="clearAllTeeth()" class="btn btn-sm btn-danger">مسح الكل</button>
                    <button type="button" onclick="selectUpperJaw()" class="btn btn-sm btn-info">الفك العلوي فقط</button>
                    <button type="button" onclick="selectLowerJaw()" class="btn btn-sm btn-info">الفك السفلي فقط</button>
                </div>
            </div>
        </div>
    `;

    // إضافة معالجات النقر للأسنان
    toothDiagram.addEventListener('click', function(e) {
        if (e.target.classList.contains('tooth') || e.target.closest('.tooth')) {
            const toothElement = e.target.classList.contains('tooth') ? e.target : e.target.closest('.tooth');
            const toothNumber = parseInt(toothElement.dataset.number);

            if (selectedTeeth.includes(toothNumber)) {
                // إزالة السن
                selectedTeeth = selectedTeeth.filter(t => t !== toothNumber);
                toothElement.classList.remove('selected');
            } else {
                // إضافة السن
                selectedTeeth.push(toothNumber);
                toothElement.classList.add('selected');
            }

            updateSelectedTeeth(selectedTeeth);
            updateSelectionDisplay(selectedTeeth);
        }
    });
}

/**
 * توليد HTML للأسنان
 * Generate HTML for teeth
 */
function generateTeethHTML(start, end, section) {
    let html = '';
    const step = start > end ? -1 : 1;

    for (let i = start; step > 0 ? i <= end : i >= end; i += step) {
        const displayNumber = i; // رقم العرض 1-8
        const uniqueId = generateUniqueToothId(section, displayNumber);
        const toothType = getToothType(displayNumber);
        const toothIcon = getToothIcon(toothType);
        const toothName = getToothName(displayNumber);

        html += `
            <div class="tooth ${toothType}" data-number="${uniqueId}" data-display="${displayNumber}" data-section="${section}"
                 title="السن رقم ${displayNumber} - ${toothName} (${getSectionName(section)})"
                 onclick="toggleToothSelection(${uniqueId})">
                <div class="tooth-icon">${toothIcon}</div>
                <div class="tooth-number">${displayNumber}</div>
            </div>
        `;
    }

    return html;
}

/**
 * توليد معرف فريد للسن
 * Generate unique tooth ID
 */
function generateUniqueToothId(section, displayNumber) {
    const sectionCodes = {
        'upper-right': 10,
        'upper-left': 20,
        'lower-right': 40,
        'lower-left': 30
    };
    
    return sectionCodes[section] + displayNumber;
}

/**
 * الحصول على اسم القسم
 * Get section name
 */
function getSectionName(section) {
    const names = {
        'upper-right': 'فك علوي يمين',
        'upper-left': 'فك علوي يسار',
        'lower-right': 'فك سفلي يمين',
        'lower-left': 'فك سفلي يسار'
    };
    
    return names[section] || section;
}

/**
 * تحديد نوع السن حسب الرقم
 * Get tooth type based on number
 */
function getToothType(number) {
    if (number === 1 || number === 2) return 'incisor';      // قواطع
    if (number === 3) return 'canine';                        // أنياب
    if (number === 4 || number === 5) return 'premolar';     // ضواحك
    if (number === 6 || number === 7 || number === 8) return 'molar'; // أضراس
    
    return 'tooth';
}

/**
 * الحصول على أيقونة السن
 * Get tooth icon
 */
function getToothIcon(type) {
    const icons = {
        'incisor': '🦷',   // قواطع
        'canine': '🦷',    // أنياب
        'premolar': '🦷',  // ضواحك
        'molar': '🦷',     // أضراس
        'tooth': '🦷'      // افتراضي
    };
    
    return icons[type] || '🦷';
}

/**
 * الحصول على اسم السن بالعربية
 * Get tooth name in Arabic
 */
function getToothName(number) {
    const names = {
        1: 'قاطع مركزي',
        2: 'قاطع جانبي',
        3: 'ناب',
        4: 'ضاحك أول',
        5: 'ضاحك ثاني',
        6: 'ضرس أول',
        7: 'ضرس ثاني',
        8: 'ضرس العقل'
    };
    
    return names[number] || 'سن';
}

/**
 * تحديث الأسنان المختارة
 * Update selected teeth
 */
function updateSelectedTeeth(selectedTeeth) {
    selectedTeeth.sort((a, b) => a - b);
    
    // تحديث حقول الإدخال إذا كانت موجودة
    const selectedTeethInput = document.getElementById('selected-teeth');
    const teethCountInput = document.getElementById('teeth-count');
    
    if (selectedTeethInput) {
        selectedTeethInput.value = selectedTeeth.join(', ');
    }
    
    if (teethCountInput) {
        teethCountInput.value = selectedTeeth.length;
    }
    
    // إعادة حساب السعر إذا كانت الدالة موجودة
    if (typeof calculateTotal === 'function') {
        calculateTotal();
    }
}

/**
 * تحديث عرض الاختيار
 * Update selection display
 */
function updateSelectionDisplay(selectedTeeth) {
    const displayElement = document.getElementById('selected-teeth-display');
    const countElement = document.getElementById('selection-count');
    
    if (!displayElement || !countElement) return;
    
    if (selectedTeeth.length === 0) {
        displayElement.innerHTML = 'لم يتم اختيار أي أسنان بعد';
        displayElement.className = 'selected-display empty';
    } else {
        selectedTeeth.sort((a, b) => a - b);
        const teethList = selectedTeeth.map(toothId => {
            const toothElement = document.querySelector(`[data-number="${toothId}"]`);
            const displayNumber = toothElement ? toothElement.dataset.display : toothId;
            const section = toothElement ? toothElement.dataset.section : '';
            const sectionLabel = getSectionLabel(section);
            
            return `<span class="selected-tooth" data-tooth="${toothId}">
                🦷 ${displayNumber} ${sectionLabel}
                <button onclick="removeTooth(${toothId})" class="remove-tooth">×</button>
            </span>`;
        }).join('');
        
        displayElement.innerHTML = teethList;
        displayElement.className = 'selected-display filled';
    }
    
    countElement.textContent = selectedTeeth.length;
}

/**
 * الحصول على تسمية القسم المختصرة
 * Get short section label
 */
function getSectionLabel(section) {
    const labels = {
        'upper-right': '(ع.ي)',
        'upper-left': '(ع.س)',
        'lower-right': '(س.ي)',
        'lower-left': '(س.س)'
    };
    
    return labels[section] || '';
}

/**
 * وظائف الاختيار السريع
 * Quick Selection Functions
 */

// اختيار جميع الأسنان
function selectAllTeeth() {
    const allTeeth = [];
    // الفك العلوي: 11-18, 21-28
    for (let i = 11; i <= 18; i++) allTeeth.push(i);
    for (let i = 21; i <= 28; i++) allTeeth.push(i);
    // الفك السفلي: 31-38, 41-48
    for (let i = 31; i <= 38; i++) allTeeth.push(i);
    for (let i = 41; i <= 48; i++) allTeeth.push(i);

    selectTeethArray(allTeeth);
}

// مسح جميع الأسنان
function clearAllTeeth() {
    selectTeethArray([]);
}

// اختيار الفك العلوي فقط
function selectUpperJaw() {
    const upperTeeth = [];
    for (let i = 11; i <= 18; i++) upperTeeth.push(i);
    for (let i = 21; i <= 28; i++) upperTeeth.push(i);
    selectTeethArray(upperTeeth);
}

// اختيار الفك السفلي فقط
function selectLowerJaw() {
    const lowerTeeth = [];
    for (let i = 31; i <= 38; i++) lowerTeeth.push(i);
    for (let i = 41; i <= 48; i++) lowerTeeth.push(i);
    selectTeethArray(lowerTeeth);
}

// اختيار مصفوفة من الأسنان
function selectTeethArray(teethArray) {
    // مسح جميع الاختيارات أولاً
    document.querySelectorAll('.tooth.selected').forEach(tooth => {
        tooth.classList.remove('selected');
    });

    // اختيار الأسنان الجديدة
    teethArray.forEach(toothNumber => {
        const toothElement = document.querySelector(`[data-number="${toothNumber}"]`);
        if (toothElement) {
            toothElement.classList.add('selected');
        }
    });

    selectedTeeth = teethArray;
    updateSelectedTeeth(selectedTeeth);
    updateSelectionDisplay(selectedTeeth);
}

// تبديل اختيار السن
function toggleToothSelection(toothNumber) {
    const toothElement = document.querySelector(`[data-number="${toothNumber}"]`);
    if (!toothElement) return;

    if (toothElement.classList.contains('selected')) {
        // إلغاء اختيار السن
        toothElement.classList.remove('selected');
        selectedTeeth = selectedTeeth.filter(t => t !== toothNumber);
    } else {
        // اختيار السن
        toothElement.classList.add('selected');
        selectedTeeth.push(toothNumber);
    }

    updateSelectedTeeth(selectedTeeth);
    updateSelectionDisplay(selectedTeeth);
}

// إزالة سن محدد
function removeTooth(toothNumber) {
    const toothElement = document.querySelector(`[data-number="${toothNumber}"]`);
    if (toothElement) {
        toothElement.classList.remove('selected');
        selectedTeeth = selectedTeeth.filter(t => t !== toothNumber);
        updateSelectedTeeth(selectedTeeth);
        updateSelectionDisplay(selectedTeeth);
    }
}

// مسح اختيار الأسنان
function clearToothSelection() {
    const teeth = document.querySelectorAll('.tooth.selected');
    teeth.forEach(tooth => tooth.classList.remove('selected'));
    selectedTeeth = [];

    const selectedTeethInput = document.getElementById('selected-teeth');
    const teethCountInput = document.getElementById('teeth-count');
    const totalPriceInput = document.getElementById('total-price');

    if (selectedTeethInput) selectedTeethInput.value = '';
    if (teethCountInput) teethCountInput.value = '';
    if (totalPriceInput) totalPriceInput.value = '';

    updateSelectionDisplay(selectedTeeth);
}

/**
 * وظائف البيانات
 * Data Functions
 */

// الحصول على الأسنان المختارة
function getSelectedTeeth() {
    return selectedTeeth.slice(); // إرجاع نسخة من المصفوفة
}

// تعيين الأسنان المختارة
function setSelectedTeeth(teethArray) {
    selectTeethArray(teethArray);
}

// الحصول على عدد الأسنان المختارة
function getSelectedTeethCount() {
    return selectedTeeth.length;
}

// الحصول على الأسنان المختارة كنص
function getSelectedTeethAsString() {
    return selectedTeeth.sort((a, b) => a - b).join(', ');
}

// تحويل نص الأسنان إلى مصفوفة
function parseTeethString(teethString) {
    if (!teethString || teethString.trim() === '') return [];

    return teethString.split(',')
        .map(t => parseInt(t.trim()))
        .filter(t => !isNaN(t));
}

// تحميل الأسنان من نص
function loadTeethFromString(teethString) {
    const teethArray = parseTeethString(teethString);
    setSelectedTeeth(teethArray);
}

/**
 * وظائف التصدير والاستيراد
 * Export/Import Functions
 */

// تصدير بيانات الأسنان
function exportTeethData() {
    return {
        selectedTeeth: getSelectedTeeth(),
        count: getSelectedTeethCount(),
        teethString: getSelectedTeethAsString(),
        timestamp: new Date().toISOString()
    };
}

// استيراد بيانات الأسنان
function importTeethData(data) {
    if (data && data.selectedTeeth && Array.isArray(data.selectedTeeth)) {
        setSelectedTeeth(data.selectedTeeth);
        return true;
    }
    return false;
}

// تصدير إعدادات المخطط
function exportDiagramSettings() {
    return {
        sections: ['upper-right', 'upper-left', 'lower-right', 'lower-left'],
        teethPerSection: 8,
        numberingSystem: 'FDI', // نظام ترقيم FDI
        selectedTeeth: getSelectedTeeth()
    };
}

/**
 * وظائف المساعدة
 * Helper Functions
 */

// التحقق من صحة رقم السن
function isValidToothNumber(toothNumber) {
    const validRanges = [
        [11, 18], [21, 28], // الفك العلوي
        [31, 38], [41, 48]  // الفك السفلي
    ];

    return validRanges.some(range =>
        toothNumber >= range[0] && toothNumber <= range[1]
    );
}

// الحصول على معلومات السن
function getToothInfo(toothNumber) {
    if (!isValidToothNumber(toothNumber)) return null;

    const quadrant = Math.floor(toothNumber / 10);
    const position = toothNumber % 10;

    const quadrantNames = {
        1: 'فك علوي يمين',
        2: 'فك علوي يسار',
        3: 'فك سفلي يسار',
        4: 'فك سفلي يمين'
    };

    return {
        number: toothNumber,
        quadrant: quadrant,
        position: position,
        quadrantName: quadrantNames[quadrant],
        type: getToothType(position),
        name: getToothName(position),
        icon: getToothIcon(getToothType(position))
    };
}

// إعداد النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود عنصر مخطط الأسنان
    if (document.getElementById('tooth-diagram')) {
        setupToothDiagram();
    }
});

// تصدير الوظائف للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        setupToothDiagram,
        getSelectedTeeth,
        setSelectedTeeth,
        getSelectedTeethCount,
        getSelectedTeethAsString,
        parseTeethString,
        loadTeethFromString,
        exportTeethData,
        importTeethData,
        exportDiagramSettings,
        isValidToothNumber,
        getToothInfo,
        selectAllTeeth,
        clearAllTeeth,
        selectUpperJaw,
        selectLowerJaw,
        toggleToothSelection,
        removeTooth,
        clearToothSelection
    };
}
