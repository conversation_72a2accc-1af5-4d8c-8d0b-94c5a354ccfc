<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة معمل الأسنان</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- الأنماط -->
    <link rel="stylesheet" href="enhanced-login-ui.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="login-background"></div>
    
    <!-- أشكال عائمة -->
    <div class="floating-shapes">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <!-- حاوي تسجيل الدخول الرئيسي -->
    <div class="enhanced-login-container">
        <div class="login-card">
            <!-- رأس البطاقة -->
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-tooth"></i>
                </div>
                <h1 class="login-title">مرحباً بك</h1>
                <p class="login-subtitle">نظام إدارة معمل الأسنان المتطور</p>
            </div>

            <!-- منطقة التنبيهات -->
            <div id="alert-container"></div>

            <!-- نموذج تسجيل الدخول -->
            <form id="enhanced-login-form" class="enhanced-login-form">
                <!-- حقل اسم المستخدم -->
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div style="position: relative;">
                        <input 
                            type="text" 
                            id="username" 
                            name="username"
                            class="enhanced-input" 
                            placeholder="أدخل اسم المستخدم"
                            required
                            autocomplete="username"
                        >
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <!-- حقل كلمة المرور -->
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="password" 
                            name="password"
                            class="enhanced-input" 
                            placeholder="أدخل كلمة المرور"
                            required
                            autocomplete="current-password"
                        >
                        <i class="fas fa-lock input-icon"></i>
                        <button 
                            type="button" 
                            id="toggle-password" 
                            style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: #a0aec0; cursor: pointer; font-size: 1rem;"
                            title="إظهار/إخفاء كلمة المرور"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    
                    <!-- مؤشر قوة كلمة المرور -->
                    <div id="password-strength" class="password-strength"></div>
                </div>

                <!-- خيارات إضافية -->
                <div class="login-options">
                    <label class="remember-me">
                        <div id="remember-checkbox" class="custom-checkbox"></div>
                        <span>تذكرني</span>
                    </label>
                    
                    <a href="#" class="forgot-password" id="forgot-password-link">
                        نسيت كلمة المرور؟
                    </a>
                </div>

                <!-- زر تسجيل الدخول -->
                <button type="submit" id="login-btn" class="enhanced-login-btn">
                    <i class="fas fa-sign-in-alt" style="margin-left: 0.5rem;"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- قسم إعادة تعيين قاعدة البيانات -->
            <div class="reset-section">
                <p>
                    <i class="fas fa-exclamation-triangle" style="margin-left: 0.5rem;"></i>
                    إذا واجهت مشكلة في تسجيل الدخول، يمكنك إعادة تعيين قاعدة البيانات
                </p>
                <button type="button" id="reset-database-btn" class="reset-btn">
                    <i class="fas fa-database" style="margin-left: 0.5rem;"></i>
                    إعادة تعيين قاعدة البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- السكريبتات -->
    <script src="enhanced-auth-system.js"></script>
    <script>
        // متغيرات عامة
        let rememberMe = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginPage();
        });

        /**
         * تهيئة صفحة تسجيل الدخول
         */
        function initializeLoginPage() {
            setupEventListeners();
            checkExistingSession();
            setupPasswordStrengthIndicator();
            setupRememberMeCheckbox();
            setupPasswordToggle();
        }

        /**
         * إعداد مستمعي الأحداث
         */
        function setupEventListeners() {
            // نموذج تسجيل الدخول
            document.getElementById('enhanced-login-form').addEventListener('submit', handleLogin);
            
            // إعادة تعيين قاعدة البيانات
            document.getElementById('reset-database-btn').addEventListener('click', handleDatabaseReset);
            
            // نسيان كلمة المرور
            document.getElementById('forgot-password-link').addEventListener('click', handleForgotPassword);
            
            // تحديث نشاط الجلسة عند التفاعل
            document.addEventListener('click', () => authSystem.updateActivity());
            document.addEventListener('keypress', () => authSystem.updateActivity());
        }

        /**
         * التحقق من وجود جلسة سابقة
         */
        function checkExistingSession() {
            if (authSystem.isLoggedIn()) {
                showAlert('تم تسجيل الدخول مسبقاً، جاري التوجيه...', 'success');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            }
        }

        /**
         * معالجة تسجيل الدخول
         */
        async function handleLogin(event) {
            event.preventDefault();
            
            const loginBtn = document.getElementById('login-btn');
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            // التحقق من صحة البيانات
            if (!username || !password) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }
            
            // إظهار حالة التحميل
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
            
            try {
                // محاولة تسجيل الدخول
                const result = await authSystem.login(username, password, rememberMe);
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    
                    // توجيه للصفحة الرئيسية بعد تأخير قصير
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showAlert(result.message, 'error');
                }
                
            } catch (error) {
                showAlert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'error');
                console.error('خطأ في تسجيل الدخول:', error);
            } finally {
                // إخفاء حالة التحميل
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
            }
        }

        /**
         * إعداد مؤشر قوة كلمة المرور
         */
        function setupPasswordStrengthIndicator() {
            const passwordInput = document.getElementById('password');
            const strengthIndicator = document.getElementById('password-strength');
            
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                
                if (password.length === 0) {
                    strengthIndicator.classList.remove('visible');
                    return;
                }
                
                const validation = authSystem.validatePasswordStrength(password);
                const strength = validation.strength;
                
                strengthIndicator.textContent = `قوة كلمة المرور: ${strength.text}`;
                strengthIndicator.className = `password-strength visible ${strength.level}`;
            });
        }

        /**
         * إعداد خانة اختيار "تذكرني"
         */
        function setupRememberMeCheckbox() {
            const checkbox = document.getElementById('remember-checkbox');
            
            checkbox.addEventListener('click', function() {
                rememberMe = !rememberMe;
                
                if (rememberMe) {
                    this.classList.add('checked');
                } else {
                    this.classList.remove('checked');
                }
            });
        }

        /**
         * إعداد زر إظهار/إخفاء كلمة المرور
         */
        function setupPasswordToggle() {
            const toggleBtn = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');
            
            toggleBtn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    passwordInput.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            });
        }

        /**
         * معالجة إعادة تعيين قاعدة البيانات
         */
        function handleDatabaseReset() {
            if (confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
                try {
                    // مسح جميع البيانات
                    localStorage.clear();
                    
                    showAlert('تم إعادة تعيين قاعدة البيانات بنجاح', 'success');
                    
                    // إعادة تحميل الصفحة بعد تأخير قصير
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                    
                } catch (error) {
                    showAlert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات', 'error');
                    console.error('خطأ في إعادة التعيين:', error);
                }
            }
        }

        /**
         * معالجة نسيان كلمة المرور
         */
        function handleForgotPassword(event) {
            event.preventDefault();
            
            showAlert('ميزة استرداد كلمة المرور ستكون متاحة قريباً', 'info');
        }

        /**
         * عرض رسالة تنبيه
         */
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alert-container');
            
            // إزالة التنبيهات السابقة
            container.innerHTML = '';
            
            // إنشاء عنصر التنبيه
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            // إضافة الأيقونة المناسبة
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            alert.innerHTML = `
                <i class="${icons[type]}"></i>
                <span>${message}</span>
            `;
            
            container.appendChild(alert);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوانٍ
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
