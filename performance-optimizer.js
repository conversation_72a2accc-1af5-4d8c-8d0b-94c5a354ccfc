/**
 * محسن الأداء المتقدم
 * Advanced Performance Optimizer
 * 
 * يحسن أداء النظام ويدير الذاكرة والموارد بذكاء
 */

class PerformanceOptimizer {
    constructor() {
        this.isOptimizing = false;
        this.performanceMetrics = {};
        this.memoryUsage = {};
        this.loadTimes = {};
        this.observers = {};
        
        this.initializePerformanceMonitoring();
        this.setupLazyLoading();
        this.setupVirtualScrolling();
        this.setupMemoryManagement();
        this.setupCaching();
    }

    /**
     * تهيئة مراقبة الأداء
     */
    initializePerformanceMonitoring() {
        // مراقبة أداء التحميل
        if ('performance' in window) {
            this.monitorPageLoad();
            this.monitorResourceLoading();
            this.monitorUserInteractions();
        }

        // مراقبة استخدام الذاكرة
        if ('memory' in performance) {
            this.monitorMemoryUsage();
        }

        // مراقبة FPS
        this.monitorFrameRate();
    }

    /**
     * مراقبة تحميل الصفحة
     */
    monitorPageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            
            this.performanceMetrics.pageLoad = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart,
                dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
                tcpConnection: navigation.connectEnd - navigation.connectStart,
                serverResponse: navigation.responseEnd - navigation.requestStart,
                domProcessing: navigation.domComplete - navigation.domLoading
            };

            this.analyzePerformance();
        });
    }

    /**
     * مراقبة تحميل الموارد
     */
    monitorResourceLoading() {
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach(entry => {
                if (entry.entryType === 'resource') {
                    this.loadTimes[entry.name] = {
                        duration: entry.duration,
                        size: entry.transferSize || 0,
                        type: this.getResourceType(entry.name)
                    };
                }
            });
        });

        observer.observe({ entryTypes: ['resource'] });
        this.observers.resource = observer;
    }

    /**
     * مراقبة تفاعلات المستخدم
     */
    monitorUserInteractions() {
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach(entry => {
                if (entry.entryType === 'measure') {
                    this.performanceMetrics.interactions = this.performanceMetrics.interactions || [];
                    this.performanceMetrics.interactions.push({
                        name: entry.name,
                        duration: entry.duration,
                        startTime: entry.startTime
                    });
                }
            });
        });

        observer.observe({ entryTypes: ['measure'] });
        this.observers.measure = observer;
    }

    /**
     * مراقبة استخدام الذاكرة
     */
    monitorMemoryUsage() {
        const updateMemoryInfo = () => {
            if (performance.memory) {
                this.memoryUsage = {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    percentage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
                };

                // تحذير عند ارتفاع استخدام الذاكرة
                if (this.memoryUsage.percentage > 80) {
                    this.handleHighMemoryUsage();
                }
            }
        };

        updateMemoryInfo();
        setInterval(updateMemoryInfo, 5000); // كل 5 ثواني
    }

    /**
     * مراقبة معدل الإطارات
     */
    monitorFrameRate() {
        let lastTime = performance.now();
        let frameCount = 0;
        let fps = 0;

        const measureFPS = (currentTime) => {
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                this.performanceMetrics.fps = fps;
                
                frameCount = 0;
                lastTime = currentTime;

                // تحذير عند انخفاض FPS
                if (fps < 30) {
                    this.handleLowFrameRate();
                }
            }

            requestAnimationFrame(measureFPS);
        };

        requestAnimationFrame(measureFPS);
    }

    /**
     * إعداد التحميل الكسول
     */
    setupLazyLoading() {
        // تحميل كسول للصور
        this.setupImageLazyLoading();
        
        // تحميل كسول للوحدات
        this.setupModuleLazyLoading();
        
        // تحميل كسول للمحتوى
        this.setupContentLazyLoading();
    }

    /**
     * تحميل كسول للصور
     */
    setupImageLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                            img.removeAttribute('data-srcset');
                        }
                        
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // مراقبة الصور الكسولة
            document.querySelectorAll('img[data-src], img.lazy').forEach(img => {
                imageObserver.observe(img);
            });

            this.observers.image = imageObserver;
        }
    }

    /**
     * تحميل كسول للوحدات
     */
    setupModuleLazyLoading() {
        this.moduleCache = new Map();
        
        // تحميل الوحدات عند الحاجة فقط
        this.loadModule = async (moduleName) => {
            if (this.moduleCache.has(moduleName)) {
                return this.moduleCache.get(moduleName);
            }

            performance.mark(`module-${moduleName}-start`);
            
            try {
                let module;
                
                switch (moduleName) {
                    case 'dashboard':
                        module = await this.loadDashboardModule();
                        break;
                    case 'doctors':
                        module = await this.loadDoctorsModule();
                        break;
                    case 'employees':
                        module = await this.loadEmployeesModule();
                        break;
                    case 'prostheses':
                        module = await this.loadProsthesesModule();
                        break;
                    case 'financial':
                        module = await this.loadFinancialModule();
                        break;
                    case 'reports':
                        module = await this.loadReportsModule();
                        break;
                    default:
                        throw new Error(`Unknown module: ${moduleName}`);
                }

                this.moduleCache.set(moduleName, module);
                
                performance.mark(`module-${moduleName}-end`);
                performance.measure(`module-${moduleName}-load`, `module-${moduleName}-start`, `module-${moduleName}-end`);
                
                return module;
            } catch (error) {
                console.error(`Failed to load module ${moduleName}:`, error);
                throw error;
            }
        };
    }

    /**
     * تحميل كسول للمحتوى
     */
    setupContentLazyLoading() {
        if ('IntersectionObserver' in window) {
            const contentObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const contentType = element.dataset.lazyContent;
                        
                        this.loadLazyContent(element, contentType);
                        contentObserver.unobserve(element);
                    }
                });
            }, {
                rootMargin: '100px 0px',
                threshold: 0.1
            });

            // مراقبة العناصر ذات المحتوى الكسول
            document.querySelectorAll('[data-lazy-content]').forEach(element => {
                contentObserver.observe(element);
            });

            this.observers.content = contentObserver;
        }
    }

    /**
     * إعداد التمرير الافتراضي
     */
    setupVirtualScrolling() {
        this.virtualScrollContainers = new Map();
        
        // تطبيق التمرير الافتراضي على القوائم الطويلة
        document.querySelectorAll('[data-virtual-scroll]').forEach(container => {
            this.enableVirtualScrolling(container);
        });
    }

    /**
     * تفعيل التمرير الافتراضي
     */
    enableVirtualScrolling(container) {
        const itemHeight = parseInt(container.dataset.itemHeight) || 50;
        const bufferSize = parseInt(container.dataset.bufferSize) || 5;
        
        const virtualScroll = {
            container,
            itemHeight,
            bufferSize,
            items: [],
            visibleItems: [],
            scrollTop: 0,
            containerHeight: container.clientHeight
        };

        // إعداد مستمع التمرير
        container.addEventListener('scroll', this.debounce(() => {
            this.updateVirtualScroll(virtualScroll);
        }, 16)); // 60 FPS

        this.virtualScrollContainers.set(container, virtualScroll);
    }

    /**
     * تحديث التمرير الافتراضي
     */
    updateVirtualScroll(virtualScroll) {
        const { container, itemHeight, bufferSize, items } = virtualScroll;
        
        virtualScroll.scrollTop = container.scrollTop;
        virtualScroll.containerHeight = container.clientHeight;
        
        const startIndex = Math.max(0, Math.floor(virtualScroll.scrollTop / itemHeight) - bufferSize);
        const endIndex = Math.min(items.length - 1, 
            Math.ceil((virtualScroll.scrollTop + virtualScroll.containerHeight) / itemHeight) + bufferSize);
        
        // إزالة العناصر غير المرئية
        virtualScroll.visibleItems.forEach(item => {
            if (item.index < startIndex || item.index > endIndex) {
                item.element.remove();
            }
        });
        
        // إضافة العناصر المرئية الجديدة
        virtualScroll.visibleItems = [];
        for (let i = startIndex; i <= endIndex; i++) {
            if (items[i]) {
                const element = this.createVirtualScrollItem(items[i], i, itemHeight);
                container.appendChild(element);
                virtualScroll.visibleItems.push({ index: i, element });
            }
        }
    }

    /**
     * إنشاء عنصر تمرير افتراضي
     */
    createVirtualScrollItem(data, index, itemHeight) {
        const element = document.createElement('div');
        element.className = 'virtual-scroll-item';
        element.style.cssText = `
            position: absolute;
            top: ${index * itemHeight}px;
            height: ${itemHeight}px;
            width: 100%;
        `;
        element.innerHTML = this.renderVirtualScrollItem(data);
        return element;
    }

    /**
     * إعداد إدارة الذاكرة
     */
    setupMemoryManagement() {
        // تنظيف الذاكرة دورياً
        setInterval(() => {
            this.cleanupMemory();
        }, 30000); // كل 30 ثانية

        // تنظيف عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.cleanupMemory();
            }
        });
    }

    /**
     * تنظيف الذاكرة
     */
    cleanupMemory() {
        // تنظيف ذاكرة التخزين المؤقت
        this.cleanupCache();
        
        // تنظيف المراقبين غير المستخدمين
        this.cleanupObservers();
        
        // تنظيف العناصر المنفصلة
        this.cleanupDetachedElements();
        
        // إجبار جمع القمامة (إذا كان متاحاً)
        if (window.gc) {
            window.gc();
        }
    }

    /**
     * إعداد التخزين المؤقت
     */
    setupCaching() {
        this.cache = new Map();
        this.cacheSize = 0;
        this.maxCacheSize = 50 * 1024 * 1024; // 50 MB
        
        // تخزين مؤقت للبيانات
        this.dataCache = new Map();
        
        // تخزين مؤقت للصور
        this.imageCache = new Map();
        
        // تخزين مؤقت للوحدات
        this.moduleCache = new Map();
    }

    /**
     * معالجة ارتفاع استخدام الذاكرة
     */
    handleHighMemoryUsage() {
        console.warn('High memory usage detected:', this.memoryUsage);
        
        // تنظيف فوري للذاكرة
        this.cleanupMemory();
        
        // تقليل جودة الرسوم المتحركة
        this.reduceAnimationQuality();
        
        // تقليل حجم التخزين المؤقت
        this.reduceCacheSize();
        
        // إرسال تنبيه
        document.dispatchEvent(new CustomEvent('highMemoryUsage', {
            detail: this.memoryUsage
        }));
    }

    /**
     * معالجة انخفاض معدل الإطارات
     */
    handleLowFrameRate() {
        console.warn('Low frame rate detected:', this.performanceMetrics.fps);
        
        // تقليل الرسوم المتحركة
        this.reduceAnimations();
        
        // تأجيل المهام غير الضرورية
        this.deferNonCriticalTasks();
        
        // إرسال تنبيه
        document.dispatchEvent(new CustomEvent('lowFrameRate', {
            detail: { fps: this.performanceMetrics.fps }
        }));
    }

    /**
     * تحليل الأداء
     */
    analyzePerformance() {
        const metrics = this.performanceMetrics.pageLoad;
        const recommendations = [];

        if (metrics.totalTime > 3000) {
            recommendations.push('Page load time is slow. Consider optimizing resources.');
        }

        if (metrics.dnsLookup > 200) {
            recommendations.push('DNS lookup is slow. Consider using a faster DNS provider.');
        }

        if (metrics.serverResponse > 1000) {
            recommendations.push('Server response is slow. Consider optimizing backend performance.');
        }

        if (metrics.domProcessing > 1000) {
            recommendations.push('DOM processing is slow. Consider reducing DOM complexity.');
        }

        this.performanceMetrics.recommendations = recommendations;
        
        // إرسال تقرير الأداء
        document.dispatchEvent(new CustomEvent('performanceAnalysis', {
            detail: this.performanceMetrics
        }));
    }

    /**
     * تقليل جودة الرسوم المتحركة
     */
    reduceAnimationQuality() {
        document.documentElement.style.setProperty('--transition-fast', '0.1s ease');
        document.documentElement.style.setProperty('--transition-normal', '0.15s ease');
        document.documentElement.style.setProperty('--transition-slow', '0.2s ease');
    }

    /**
     * تقليل الرسوم المتحركة
     */
    reduceAnimations() {
        document.body.classList.add('reduced-motion');
    }

    /**
     * تأجيل المهام غير الضرورية
     */
    deferNonCriticalTasks() {
        // تأجيل تحديث الإحصائيات
        clearInterval(this.statsUpdateInterval);
        this.statsUpdateInterval = setInterval(() => {
            this.updateStats();
        }, 10000); // كل 10 ثواني بدلاً من 5

        // تأجيل تحديث الرسوم البيانية
        this.deferChartUpdates = true;
    }

    /**
     * تنظيف التخزين المؤقت
     */
    cleanupCache() {
        // تنظيف التخزين المؤقت القديم
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30 دقيقة

        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > maxAge) {
                this.cache.delete(key);
                this.cacheSize -= value.size || 0;
            }
        }

        // تنظيف تخزين الصور المؤقت
        for (const [key, value] of this.imageCache.entries()) {
            if (now - value.timestamp > maxAge) {
                this.imageCache.delete(key);
            }
        }
    }

    /**
     * تنظيف المراقبين
     */
    cleanupObservers() {
        // إيقاف المراقبين غير المستخدمين
        Object.values(this.observers).forEach(observer => {
            if (observer && typeof observer.disconnect === 'function') {
                // التحقق من وجود عناصر مراقبة
                const hasTargets = observer.takeRecords && observer.takeRecords().length > 0;
                if (!hasTargets) {
                    observer.disconnect();
                }
            }
        });
    }

    /**
     * تنظيف العناصر المنفصلة
     */
    cleanupDetachedElements() {
        // إزالة مستمعي الأحداث من العناصر المحذوفة
        document.querySelectorAll('[data-cleanup]').forEach(element => {
            if (!element.isConnected) {
                element.remove();
            }
        });
    }

    /**
     * تقليل حجم التخزين المؤقت
     */
    reduceCacheSize() {
        this.maxCacheSize = Math.max(10 * 1024 * 1024, this.maxCacheSize * 0.5); // تقليل إلى النصف
        
        // حذف العناصر الأقل استخداماً
        const sortedEntries = Array.from(this.cache.entries())
            .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
        
        while (this.cacheSize > this.maxCacheSize && sortedEntries.length > 0) {
            const [key, value] = sortedEntries.shift();
            this.cache.delete(key);
            this.cacheSize -= value.size || 0;
        }
    }

    /**
     * الحصول على نوع المورد
     */
    getResourceType(url) {
        const extension = url.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
            return 'image';
        }
        if (['css'].includes(extension)) {
            return 'stylesheet';
        }
        if (['js'].includes(extension)) {
            return 'script';
        }
        if (['woff', 'woff2', 'ttf', 'otf'].includes(extension)) {
            return 'font';
        }
        
        return 'other';
    }

    /**
     * تأخير التنفيذ
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * الحصول على تقرير الأداء
     */
    getPerformanceReport() {
        return {
            metrics: this.performanceMetrics,
            memory: this.memoryUsage,
            loadTimes: this.loadTimes,
            cacheStats: {
                size: this.cacheSize,
                maxSize: this.maxCacheSize,
                entries: this.cache.size
            }
        };
    }

    /**
     * تحسين الأداء العام
     */
    optimize() {
        if (this.isOptimizing) return;
        
        this.isOptimizing = true;
        
        try {
            // تنظيف الذاكرة
            this.cleanupMemory();
            
            // تحسين DOM
            this.optimizeDOM();
            
            // تحسين الأحداث
            this.optimizeEvents();
            
            // تحسين الرسوم المتحركة
            this.optimizeAnimations();
            
            console.log('Performance optimization completed');
        } catch (error) {
            console.error('Performance optimization failed:', error);
        } finally {
            this.isOptimizing = false;
        }
    }

    /**
     * تحسين DOM
     */
    optimizeDOM() {
        // إزالة العقد الفارغة
        document.querySelectorAll('*').forEach(element => {
            if (element.childNodes.length === 0 && element.textContent.trim() === '') {
                element.remove();
            }
        });
        
        // دمج العقد المتشابهة
        this.mergeAdjacentTextNodes();
    }

    /**
     * دمج العقد النصية المتجاورة
     */
    mergeAdjacentTextNodes() {
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        const textNodes = [];
        let node;
        
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        
        textNodes.forEach(textNode => {
            if (textNode.nextSibling && textNode.nextSibling.nodeType === Node.TEXT_NODE) {
                textNode.textContent += textNode.nextSibling.textContent;
                textNode.nextSibling.remove();
            }
        });
    }

    /**
     * تحسين الأحداث
     */
    optimizeEvents() {
        // استخدام تفويض الأحداث
        this.setupEventDelegation();
        
        // إزالة مستمعي الأحداث غير المستخدمين
        this.removeUnusedEventListeners();
    }

    /**
     * إعداد تفويض الأحداث
     */
    setupEventDelegation() {
        // تفويض أحداث النقر
        document.body.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (target) {
                const action = target.dataset.action;
                this.handleDelegatedAction(action, target, e);
            }
        });
    }

    /**
     * معالجة الإجراءات المفوضة
     */
    handleDelegatedAction(action, target, event) {
        switch (action) {
            case 'toggle-sidebar':
                this.toggleSidebar();
                break;
            case 'close-modal':
                this.closeModal(target.closest('.modal'));
                break;
            case 'submit-form':
                this.submitForm(target.closest('form'));
                break;
            // إضافة المزيد من الإجراءات حسب الحاجة
        }
    }

    /**
     * تحسين الرسوم المتحركة
     */
    optimizeAnimations() {
        // استخدام requestAnimationFrame للرسوم المتحركة
        this.setupRAFAnimations();
        
        // تحسين CSS animations
        this.optimizeCSSAnimations();
    }

    /**
     * إعداد رسوم متحركة بـ RAF
     */
    setupRAFAnimations() {
        this.animationQueue = [];
        this.isAnimating = false;
        
        this.processAnimationQueue = () => {
            if (this.animationQueue.length === 0) {
                this.isAnimating = false;
                return;
            }
            
            const animations = this.animationQueue.splice(0);
            animations.forEach(animation => {
                animation();
            });
            
            requestAnimationFrame(this.processAnimationQueue);
        };
    }

    /**
     * إضافة رسم متحرك للطابور
     */
    queueAnimation(animationFunction) {
        this.animationQueue.push(animationFunction);
        
        if (!this.isAnimating) {
            this.isAnimating = true;
            requestAnimationFrame(this.processAnimationQueue);
        }
    }
}

// إنشاء مثيل عام من محسن الأداء
const performanceOptimizer = new PerformanceOptimizer();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PerformanceOptimizer, performanceOptimizer };
}
