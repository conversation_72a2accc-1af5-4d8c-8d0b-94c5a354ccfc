/**
 * نظام التقارير والإحصائيات المتقدم
 * Enhanced Reports and Analytics System
 * 
 * المميزات:
 * - تقارير تفاعلية مع رسوم بيانية متقدمة
 * - تصدير متعدد الصيغ (PDF, Excel, CSV)
 * - تحليلات ذكية ومؤشرات الأداء
 * - تقارير مجدولة وتلقائية
 * - لوحات معلومات قابلة للتخصيص
 * - تحليل الاتجاهات والتنبؤات
 */

class EnhancedReportsSystem {
    constructor() {
        this.reports = [];
        this.reportTemplates = [];
        this.scheduledReports = [];
        this.dashboards = [];
        this.kpis = [];
        this.loadData();
        this.initializeReportTemplates();
        this.initializeKPIs();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        this.reports = data.reports || [];
        this.reportTemplates = data.reportTemplates || [];
        this.scheduledReports = data.scheduledReports || [];
        this.dashboards = data.dashboards || [];
        this.kpis = data.kpis || [];
    }

    /**
     * حفظ البيانات
     */
    saveData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        data.reports = this.reports;
        data.reportTemplates = this.reportTemplates;
        data.scheduledReports = this.scheduledReports;
        data.dashboards = this.dashboards;
        data.kpis = this.kpis;
        localStorage.setItem('dental_lab_data', JSON.stringify(data));
    }

    /**
     * تهيئة قوالب التقارير
     */
    initializeReportTemplates() {
        if (this.reportTemplates.length === 0) {
            this.reportTemplates = [
                {
                    id: 1,
                    name: 'تقرير الإيرادات الشهرية',
                    category: 'financial',
                    description: 'تقرير مفصل للإيرادات والمدفوعات الشهرية',
                    fields: ['revenue', 'expenses', 'profit', 'invoices'],
                    chartTypes: ['line', 'bar', 'pie'],
                    isActive: true
                },
                {
                    id: 2,
                    name: 'تقرير أداء الأطباء',
                    category: 'doctors',
                    description: 'تحليل أداء الأطباء وإحصائيات التركيبات',
                    fields: ['prostheses_count', 'revenue_per_doctor', 'completion_rate'],
                    chartTypes: ['bar', 'radar', 'doughnut'],
                    isActive: true
                },
                {
                    id: 3,
                    name: 'تقرير إنتاجية المعمل',
                    category: 'production',
                    description: 'تحليل الإنتاجية ومراحل التصنيع',
                    fields: ['production_volume', 'completion_time', 'quality_score'],
                    chartTypes: ['line', 'area', 'bar'],
                    isActive: true
                },
                {
                    id: 4,
                    name: 'تقرير الموظفين والحضور',
                    category: 'employees',
                    description: 'إحصائيات الموظفين والحضور والأداء',
                    fields: ['attendance_rate', 'performance_score', 'salary_analysis'],
                    chartTypes: ['bar', 'line', 'heatmap'],
                    isActive: true
                },
                {
                    id: 5,
                    name: 'تقرير المخزون والمواد',
                    category: 'inventory',
                    description: 'تحليل المخزون واستهلاك المواد',
                    fields: ['stock_levels', 'consumption_rate', 'reorder_points'],
                    chartTypes: ['bar', 'line', 'gauge'],
                    isActive: true
                },
                {
                    id: 6,
                    name: 'تقرير رضا العملاء',
                    category: 'customers',
                    description: 'تحليل رضا العملاء والتقييمات',
                    fields: ['satisfaction_score', 'feedback_analysis', 'retention_rate'],
                    chartTypes: ['gauge', 'bar', 'pie'],
                    isActive: true
                }
            ];
            this.saveData();
        }
    }

    /**
     * تهيئة مؤشرات الأداء الرئيسية
     */
    initializeKPIs() {
        if (this.kpis.length === 0) {
            this.kpis = [
                {
                    id: 1,
                    name: 'الإيرادات الشهرية',
                    category: 'financial',
                    formula: 'SUM(invoices.total)',
                    target: 100000,
                    unit: 'ج.م',
                    trend: 'up',
                    isActive: true
                },
                {
                    id: 2,
                    name: 'معدل إنجاز التركيبات',
                    category: 'production',
                    formula: 'COUNT(prostheses.completed) / COUNT(prostheses.total) * 100',
                    target: 95,
                    unit: '%',
                    trend: 'up',
                    isActive: true
                },
                {
                    id: 3,
                    name: 'متوسط وقت الإنجاز',
                    category: 'production',
                    formula: 'AVG(prostheses.completion_time)',
                    target: 7,
                    unit: 'يوم',
                    trend: 'down',
                    isActive: true
                },
                {
                    id: 4,
                    name: 'معدل حضور الموظفين',
                    category: 'employees',
                    formula: 'COUNT(attendance.present) / COUNT(attendance.total) * 100',
                    target: 90,
                    unit: '%',
                    trend: 'up',
                    isActive: true
                },
                {
                    id: 5,
                    name: 'درجة رضا العملاء',
                    category: 'customers',
                    formula: 'AVG(reviews.rating)',
                    target: 4.5,
                    unit: '/5',
                    trend: 'up',
                    isActive: true
                },
                {
                    id: 6,
                    name: 'هامش الربح',
                    category: 'financial',
                    formula: '(revenue - expenses) / revenue * 100',
                    target: 25,
                    unit: '%',
                    trend: 'up',
                    isActive: true
                }
            ];
            this.saveData();
        }
    }

    /**
     * إنشاء HTML لنظام التقارير
     */
    generateReportsSystemHTML() {
        return `
            <div class="enhanced-reports-system">
                <!-- رأس النظام -->
                <div class="reports-header">
                    <div class="header-content">
                        <h1 class="reports-title">
                            <i class="fas fa-chart-bar"></i>
                            نظام التقارير والإحصائيات المتقدم
                        </h1>
                        <p class="reports-subtitle">
                            تحليلات ذكية وتقارير تفاعلية لاتخاذ قرارات مدروسة
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="reportsSystem.showCreateReportModal()">
                            <i class="fas fa-plus"></i>
                            تقرير جديد
                        </button>
                        <button class="btn btn-info" onclick="reportsSystem.showDashboardBuilder()">
                            <i class="fas fa-th-large"></i>
                            بناء لوحة معلومات
                        </button>
                        <button class="btn btn-success" onclick="reportsSystem.showScheduledReports()">
                            <i class="fas fa-clock"></i>
                            التقارير المجدولة
                        </button>
                        <button class="btn btn-secondary" onclick="reportsSystem.exportAllReports()">
                            <i class="fas fa-download"></i>
                            تصدير شامل
                        </button>
                    </div>
                </div>

                <!-- مؤشرات الأداء الرئيسية -->
                <div class="kpis-section">
                    <div class="section-header">
                        <h2>مؤشرات الأداء الرئيسية</h2>
                        <div class="kpi-controls">
                            <select id="kpi-period">
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month" selected>هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                            </select>
                            <button class="btn btn-sm btn-outline" onclick="reportsSystem.refreshKPIs()">
                                <i class="fas fa-sync-alt"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                    <div class="kpis-grid">
                        ${this.generateKPIsGrid()}
                    </div>
                </div>

                <!-- تبويبات النظام -->
                <div class="reports-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="reportsSystem.switchReportsTab('overview')">
                            <i class="fas fa-tachometer-alt"></i>
                            نظرة عامة
                        </button>
                        <button class="tab-btn" onclick="reportsSystem.switchReportsTab('financial')">
                            <i class="fas fa-chart-line"></i>
                            التقارير المالية
                        </button>
                        <button class="tab-btn" onclick="reportsSystem.switchReportsTab('production')">
                            <i class="fas fa-cogs"></i>
                            تقارير الإنتاج
                        </button>
                        <button class="tab-btn" onclick="reportsSystem.switchReportsTab('doctors')">
                            <i class="fas fa-user-md"></i>
                            تقارير الأطباء
                        </button>
                        <button class="tab-btn" onclick="reportsSystem.switchReportsTab('employees')">
                            <i class="fas fa-users"></i>
                            تقارير الموظفين
                        </button>
                        <button class="tab-btn" onclick="reportsSystem.switchReportsTab('analytics')">
                            <i class="fas fa-brain"></i>
                            التحليلات الذكية
                        </button>
                        <button class="tab-btn" onclick="reportsSystem.switchReportsTab('custom')">
                            <i class="fas fa-tools"></i>
                            تقارير مخصصة
                        </button>
                    </div>

                    <!-- تبويب النظرة العامة -->
                    <div id="overview-reports-tab" class="tab-content active">
                        ${this.generateOverviewTab()}
                    </div>

                    <!-- تبويب التقارير المالية -->
                    <div id="financial-reports-tab" class="tab-content">
                        ${this.generateFinancialReportsTab()}
                    </div>

                    <!-- تبويب تقارير الإنتاج -->
                    <div id="production-reports-tab" class="tab-content">
                        ${this.generateProductionReportsTab()}
                    </div>

                    <!-- تبويب تقارير الأطباء -->
                    <div id="doctors-reports-tab" class="tab-content">
                        ${this.generateDoctorsReportsTab()}
                    </div>

                    <!-- تبويب تقارير الموظفين -->
                    <div id="employees-reports-tab" class="tab-content">
                        ${this.generateEmployeesReportsTab()}
                    </div>

                    <!-- تبويب التحليلات الذكية -->
                    <div id="analytics-reports-tab" class="tab-content">
                        ${this.generateAnalyticsTab()}
                    </div>

                    <!-- تبويب التقارير المخصصة -->
                    <div id="custom-reports-tab" class="tab-content">
                        ${this.generateCustomReportsTab()}
                    </div>
                </div>

                <!-- النوافذ المنبثقة -->
                ${this.generateReportsModals()}
            </div>
        `;
    }

    /**
     * إنشاء شبكة مؤشرات الأداء
     */
    generateKPIsGrid() {
        return this.kpis.filter(kpi => kpi.isActive).map(kpi => {
            const currentValue = this.calculateKPIValue(kpi);
            const progress = this.calculateKPIProgress(currentValue, kpi.target, kpi.trend);
            const statusClass = this.getKPIStatusClass(progress);
            
            return `
                <div class="kpi-card ${statusClass}">
                    <div class="kpi-header">
                        <div class="kpi-icon">
                            <i class="${this.getKPIIcon(kpi.category)}"></i>
                        </div>
                        <div class="kpi-trend ${kpi.trend === 'up' ? 'positive' : 'negative'}">
                            <i class="fas fa-arrow-${kpi.trend}"></i>
                        </div>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value">
                            ${this.formatKPIValue(currentValue, kpi.unit)}
                        </div>
                        <div class="kpi-name">${kpi.name}</div>
                        <div class="kpi-target">
                            الهدف: ${this.formatKPIValue(kpi.target, kpi.unit)}
                        </div>
                    </div>
                    <div class="kpi-progress">
                        <div class="progress-bar">
                            <div class="progress-fill ${statusClass}" style="width: ${Math.min(progress, 100)}%"></div>
                        </div>
                        <div class="progress-text">
                            ${progress.toFixed(1)}% من الهدف
                        </div>
                    </div>
                    <div class="kpi-actions">
                        <button class="btn btn-sm btn-outline" onclick="reportsSystem.viewKPIDetails(${kpi.id})" 
                                title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="reportsSystem.exportKPI(${kpi.id})" 
                                title="تصدير">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * إنشاء تبويب النظرة العامة
     */
    generateOverviewTab() {
        return `
            <div class="overview-content">
                <!-- الرسوم البيانية الرئيسية -->
                <div class="main-charts">
                    <div class="charts-grid">
                        <div class="chart-card large">
                            <div class="chart-header">
                                <h3>الاتجاهات العامة</h3>
                                <div class="chart-controls">
                                    <select id="overview-period">
                                        <option value="6">آخر 6 أشهر</option>
                                        <option value="12" selected>آخر 12 شهر</option>
                                        <option value="24">آخر سنتين</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="overview-trends-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card medium">
                            <div class="chart-header">
                                <h3>توزيع الإيرادات</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="revenue-distribution-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card medium">
                            <div class="chart-header">
                                <h3>أداء الأقسام</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="departments-performance-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الملخص التنفيذي -->
                <div class="executive-summary">
                    <div class="summary-header">
                        <h3>الملخص التنفيذي</h3>
                        <div class="summary-period">
                            ${this.getCurrentPeriodText()}
                        </div>
                    </div>
                    <div class="summary-content">
                        ${this.generateExecutiveSummary()}
                    </div>
                </div>

                <!-- التنبيهات والتوصيات -->
                <div class="insights-section">
                    <div class="insights-header">
                        <h3>التنبيهات والتوصيات الذكية</h3>
                        <button class="btn btn-sm btn-outline" onclick="reportsSystem.refreshInsights()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                    <div class="insights-list">
                        ${this.generateSmartInsights()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حساب قيمة مؤشر الأداء
     */
    calculateKPIValue(kpi) {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const now = new Date();
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

        switch (kpi.id) {
            case 1: // الإيرادات الشهرية
                const invoices = data.invoices || [];
                return invoices
                    .filter(inv => new Date(inv.date) >= monthStart)
                    .reduce((sum, inv) => sum + (inv.total || 0), 0);

            case 2: // معدل إنجاز التركيبات
                const prostheses = data.prostheses || [];
                const total = prostheses.length;
                const completed = prostheses.filter(p => p.status === 'completed' || p.status === 'delivered').length;
                return total > 0 ? (completed / total) * 100 : 0;

            case 3: // متوسط وقت الإنجاز
                const completedProstheses = (data.prostheses || []).filter(p => p.completedAt);
                if (completedProstheses.length === 0) return 0;
                const totalDays = completedProstheses.reduce((sum, p) => {
                    const start = new Date(p.created_at);
                    const end = new Date(p.completedAt);
                    return sum + Math.ceil((end - start) / (1000 * 60 * 60 * 24));
                }, 0);
                return totalDays / completedProstheses.length;

            case 4: // معدل حضور الموظفين
                const attendance = data.attendance || [];
                const thisMonthAttendance = attendance.filter(a => {
                    const date = new Date(a.date);
                    return date >= monthStart;
                });
                const totalAttendance = thisMonthAttendance.length;
                const presentAttendance = thisMonthAttendance.filter(a => a.status === 'present').length;
                return totalAttendance > 0 ? (presentAttendance / totalAttendance) * 100 : 0;

            case 5: // درجة رضا العملاء
                const reviews = data.reviews || [];
                if (reviews.length === 0) return 4.2; // قيمة افتراضية
                return reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

            case 6: // هامش الربح
                const revenue = this.calculateKPIValue({ id: 1 });
                const expenses = (data.expenses || [])
                    .filter(exp => new Date(exp.date) >= monthStart)
                    .reduce((sum, exp) => sum + (exp.amount || 0), 0);
                return revenue > 0 ? ((revenue - expenses) / revenue) * 100 : 0;

            default:
                return 0;
        }
    }

    /**
     * حساب تقدم مؤشر الأداء
     */
    calculateKPIProgress(currentValue, target, trend) {
        if (trend === 'down') {
            // للمؤشرات التي نريد تقليلها (مثل وقت الإنجاز)
            return target > 0 ? Math.max(0, (target - currentValue) / target * 100) : 0;
        } else {
            // للمؤشرات التي نريد زيادتها
            return target > 0 ? (currentValue / target) * 100 : 0;
        }
    }

    /**
     * الحصول على فئة حالة مؤشر الأداء
     */
    getKPIStatusClass(progress) {
        if (progress >= 100) return 'excellent';
        if (progress >= 80) return 'good';
        if (progress >= 60) return 'average';
        if (progress >= 40) return 'below-average';
        return 'poor';
    }

    /**
     * الحصول على أيقونة مؤشر الأداء
     */
    getKPIIcon(category) {
        const icons = {
            'financial': 'fas fa-dollar-sign',
            'production': 'fas fa-cogs',
            'employees': 'fas fa-users',
            'customers': 'fas fa-heart',
            'quality': 'fas fa-star'
        };
        return icons[category] || 'fas fa-chart-bar';
    }

    /**
     * تنسيق قيمة مؤشر الأداء
     */
    formatKPIValue(value, unit) {
        if (unit === 'ج.م') {
            return value.toLocaleString() + ' ' + unit;
        } else if (unit === '%') {
            return value.toFixed(1) + unit;
        } else if (unit === '/5') {
            return value.toFixed(1) + unit;
        } else {
            return value.toFixed(1) + ' ' + unit;
        }
    }

    /**
     * الحصول على نص الفترة الحالية
     */
    getCurrentPeriodText() {
        const now = new Date();
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
    }

    /**
     * إنشاء الملخص التنفيذي
     */
    generateExecutiveSummary() {
        const summary = this.calculateExecutiveSummary();

        return `
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-icon revenue">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="summary-content">
                        <h4>الأداء المالي</h4>
                        <p>الإيرادات هذا الشهر: <strong>${summary.revenue.toLocaleString()} ج.م</strong></p>
                        <p>نمو بنسبة <span class="growth ${summary.revenueGrowth >= 0 ? 'positive' : 'negative'}">
                            ${summary.revenueGrowth >= 0 ? '+' : ''}${summary.revenueGrowth.toFixed(1)}%
                        </span> مقارنة بالشهر الماضي</p>
                    </div>
                </div>

                <div class="summary-item">
                    <div class="summary-icon production">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="summary-content">
                        <h4>الإنتاجية</h4>
                        <p>تم إنجاز <strong>${summary.completedProstheses}</strong> تركيبة</p>
                        <p>بمعدل إنجاز <strong>${summary.completionRate.toFixed(1)}%</strong></p>
                    </div>
                </div>

                <div class="summary-item">
                    <div class="summary-icon quality">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="summary-content">
                        <h4>الجودة</h4>
                        <p>متوسط درجة الجودة: <strong>${summary.qualityScore.toFixed(1)}/5</strong></p>
                        <p>معدل رضا العملاء: <strong>${summary.satisfactionRate.toFixed(1)}%</strong></p>
                    </div>
                </div>

                <div class="summary-item">
                    <div class="summary-icon efficiency">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-content">
                        <h4>الكفاءة</h4>
                        <p>متوسط وقت الإنجاز: <strong>${summary.avgCompletionTime.toFixed(1)} يوم</strong></p>
                        <p>معدل الحضور: <strong>${summary.attendanceRate.toFixed(1)}%</strong></p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حساب الملخص التنفيذي
     */
    calculateExecutiveSummary() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        // الإيرادات
        const thisMonthRevenue = (data.invoices || [])
            .filter(inv => new Date(inv.date) >= thisMonth && new Date(inv.date) <= thisMonthEnd)
            .reduce((sum, inv) => sum + (inv.total || 0), 0);

        const lastMonthRevenue = (data.invoices || [])
            .filter(inv => {
                const date = new Date(inv.date);
                return date >= lastMonth && date < thisMonth;
            })
            .reduce((sum, inv) => sum + (inv.total || 0), 0);

        const revenueGrowth = lastMonthRevenue > 0 ?
            ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

        // الإنتاجية
        const prostheses = data.prostheses || [];
        const thisMonthProstheses = prostheses.filter(p =>
            new Date(p.created_at) >= thisMonth && new Date(p.created_at) <= thisMonthEnd
        );
        const completedProstheses = thisMonthProstheses.filter(p =>
            p.status === 'completed' || p.status === 'delivered'
        ).length;
        const completionRate = thisMonthProstheses.length > 0 ?
            (completedProstheses / thisMonthProstheses.length) * 100 : 0;

        // الجودة
        const reviews = data.reviews || [];
        const qualityScore = reviews.length > 0 ?
            reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length : 4.2;
        const satisfactionRate = qualityScore * 20; // تحويل من 5 إلى 100

        // الكفاءة
        const completedWithTime = prostheses.filter(p => p.completedAt && p.created_at);
        const avgCompletionTime = completedWithTime.length > 0 ?
            completedWithTime.reduce((sum, p) => {
                const start = new Date(p.created_at);
                const end = new Date(p.completedAt);
                return sum + Math.ceil((end - start) / (1000 * 60 * 60 * 24));
            }, 0) / completedWithTime.length : 0;

        const attendance = (data.attendance || []).filter(a => new Date(a.date) >= thisMonth);
        const attendanceRate = attendance.length > 0 ?
            (attendance.filter(a => a.status === 'present').length / attendance.length) * 100 : 0;

        return {
            revenue: thisMonthRevenue,
            revenueGrowth: revenueGrowth,
            completedProstheses: completedProstheses,
            completionRate: completionRate,
            qualityScore: qualityScore,
            satisfactionRate: satisfactionRate,
            avgCompletionTime: avgCompletionTime,
            attendanceRate: attendanceRate
        };
    }

    /**
     * إنشاء التنبيهات الذكية
     */
    generateSmartInsights() {
        const insights = this.calculateSmartInsights();

        if (insights.length === 0) {
            return `
                <div class="empty-insights">
                    <i class="fas fa-lightbulb"></i>
                    <p>لا توجد تنبيهات في الوقت الحالي</p>
                </div>
            `;
        }

        return insights.map(insight => `
            <div class="insight-item ${insight.type}">
                <div class="insight-icon">
                    <i class="${insight.icon}"></i>
                </div>
                <div class="insight-content">
                    <div class="insight-title">${insight.title}</div>
                    <div class="insight-description">${insight.description}</div>
                    ${insight.action ?
                        `<div class="insight-action">
                            <button class="btn btn-sm btn-outline" onclick="${insight.action}">
                                ${insight.actionText}
                            </button>
                        </div>` : ''
                    }
                </div>
                <div class="insight-priority ${insight.priority}">
                    ${insight.priority === 'high' ? 'عالي' : insight.priority === 'medium' ? 'متوسط' : 'منخفض'}
                </div>
            </div>
        `).join('');
    }

    /**
     * حساب التنبيهات الذكية
     */
    calculateSmartInsights() {
        const insights = [];
        const summary = this.calculateExecutiveSummary();

        // تنبيه انخفاض الإيرادات
        if (summary.revenueGrowth < -10) {
            insights.push({
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'انخفاض في الإيرادات',
                description: `انخفضت الإيرادات بنسبة ${Math.abs(summary.revenueGrowth).toFixed(1)}% مقارنة بالشهر الماضي`,
                priority: 'high'
            });
        }

        // تنبيه انخفاض معدل الإنجاز
        if (summary.completionRate < 80) {
            insights.push({
                type: 'warning',
                icon: 'fas fa-clock',
                title: 'انخفاض معدل الإنجاز',
                description: `معدل إنجاز التركيبات ${summary.completionRate.toFixed(1)}% أقل من المستهدف`,
                priority: 'medium'
            });
        }

        // تنبيه إيجابي للنمو
        if (summary.revenueGrowth > 15) {
            insights.push({
                type: 'success',
                icon: 'fas fa-chart-line',
                title: 'نمو ممتاز في الإيرادات',
                description: `نمو رائع بنسبة ${summary.revenueGrowth.toFixed(1)}% في الإيرادات هذا الشهر`,
                priority: 'low'
            });
        }

        return insights;
    }

    /**
     * تبديل تبويبات التقارير
     */
    switchReportsTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.reports-tabs .tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.reports-tabs .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-reports-tab`).classList.add('active');
        event.target.classList.add('active');
    }

    /**
     * تحديث مؤشرات الأداء
     */
    refreshKPIs() {
        const kpisGrid = document.querySelector('.kpis-grid');
        if (kpisGrid) {
            kpisGrid.innerHTML = this.generateKPIsGrid();
        }
    }

    /**
     * تحديث التنبيهات
     */
    refreshInsights() {
        const insightsList = document.querySelector('.insights-list');
        if (insightsList) {
            insightsList.innerHTML = this.generateSmartInsights();
        }
    }

    /**
     * عرض نافذة إنشاء تقرير
     */
    showCreateReportModal() {
        alert('ميزة إنشاء تقرير جديد ستكون متاحة قريباً');
    }

    /**
     * عرض بناء لوحة المعلومات
     */
    showDashboardBuilder() {
        alert('ميزة بناء لوحة المعلومات ستكون متاحة قريباً');
    }

    /**
     * عرض التقارير المجدولة
     */
    showScheduledReports() {
        alert('ميزة التقارير المجدولة ستكون متاحة قريباً');
    }

    /**
     * تصدير جميع التقارير
     */
    exportAllReports() {
        const exportData = {
            reports: this.reports,
            kpis: this.kpis.map(kpi => ({
                ...kpi,
                currentValue: this.calculateKPIValue(kpi),
                progress: this.calculateKPIProgress(this.calculateKPIValue(kpi), kpi.target, kpi.trend)
            })),
            summary: this.calculateExecutiveSummary(),
            insights: this.calculateSmartInsights(),
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `reports-export-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }
}

// إنشاء مثيل عام من نظام التقارير
const reportsSystem = new EnhancedReportsSystem();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedReportsSystem, reportsSystem };
}
