/**
 * أنماط النظام المالي المتكامل
 * Enhanced Financial System Styles
 */

/* الحاوي الرئيسي */
.enhanced-financial-system {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    direction: rtl;
}

/* رأس النظام المالي */
.financial-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    flex-wrap: wrap;
    gap: 1rem;
}

.financial-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.financial-title i {
    color: #667eea;
}

.financial-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* الملخص المالي */
.financial-summary {
    margin-bottom: 2rem;
}

.financial-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* ألوان البطاقات */
.summary-card.revenue { --card-color: #48bb78; }
.summary-card.expenses { --card-color: #f56565; }
.summary-card.profit { --card-color: #667eea; }
.summary-card.outstanding { --card-color: #ed8936; }
.summary-card.cash { --card-color: #4299e1; }
.summary-card.tax { --card-color: #9f7aea; }

.summary-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--card-color);
    flex-shrink: 0;
}

.summary-content {
    flex: 1;
}

.summary-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 1rem;
    color: #718096;
    margin-bottom: 0.25rem;
}

.summary-period {
    font-size: 0.875rem;
    color: #a0aec0;
}

.summary-change {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
}

.summary-change.positive { color: #38a169; }
.summary-change.negative { color: #e53e3e; }
.summary-change.neutral { color: #718096; }

/* التبويبات المالية */
.financial-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e2e8f0;
    background: #f7fafc;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #718096;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 140px;
    justify-content: center;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.tab-btn:hover {
    color: #4a5568;
    background: rgba(255, 255, 255, 0.5);
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* الرسوم البيانية المالية */
.financial-charts {
    margin-bottom: 2rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chart-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.chart-header select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.875rem;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* الأنشطة المالية الحديثة */
.recent-financial-activities {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
}

.activities-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.activities-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.activity-item:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-item.invoice .activity-icon {
    background: #bee3f8;
    color: #3182ce;
}

.activity-item.payment .activity-icon {
    background: #c6f6d5;
    color: #38a169;
}

.activity-item.expense .activity-icon {
    background: #fed7d7;
    color: #e53e3e;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-description {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0aec0;
    font-size: 0.75rem;
}

.activity-amount {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
}

/* فلاتر الفواتير */
.invoices-filters {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-bar {
    flex: 1;
    min-width: 300px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-group i {
    position: absolute;
    right: 1rem;
    color: #a0aec0;
    font-size: 1.1rem;
}

.search-input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filters-group select,
.filters-group input {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filters-group select:focus,
.filters-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* جدول الفواتير */
.invoices-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.invoices-table {
    width: 100%;
    border-collapse: collapse;
}

.invoices-table th {
    background: #f7fafc;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 2px solid #e2e8f0;
}

.invoices-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.invoice-row:hover {
    background: #f7fafc;
}

.invoice-row.overdue {
    background: linear-gradient(135deg, #fff 0%, #fed7d7 100%);
}

.invoice-number strong {
    color: #2d3748;
    font-size: 1rem;
}

.credit-note-badge {
    background: #fef5e7;
    color: #d69e2e;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: 0.5rem;
}

.client-info .client-name {
    font-weight: 600;
    color: #2d3748;
}

.client-info .client-type {
    font-size: 0.8rem;
    color: #718096;
}

.amount-info .total-amount {
    font-weight: 600;
    color: #2d3748;
}

.amount-info .remaining-amount {
    font-size: 0.8rem;
    color: #ed8936;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-draft {
    background: #e2e8f0;
    color: #718096;
}

.status-badge.status-sent {
    background: #bee3f8;
    color: #3182ce;
}

.status-badge.status-paid {
    background: #c6f6d5;
    color: #38a169;
}

.status-badge.status-partial {
    background: #fef5e7;
    color: #d69e2e;
}

.status-badge.status-overdue {
    background: #fed7d7;
    color: #e53e3e;
}

.status-badge.status-cancelled {
    background: #fed7d7;
    color: #e53e3e;
}

.overdue-badge {
    background: #f56565;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: 0.5rem;
}

.invoice-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-width: 160px;
    z-index: 1000;
    display: none;
    padding: 0.5rem 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.dropdown-menu a:hover {
    background: #f7fafc;
}

.dropdown-menu a.text-danger {
    color: #e53e3e;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 0.5rem 0;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    white-space: nowrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-1px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-1px);
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
    transform: translateY(-1px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* حالة فارغة */
.empty-state,
.empty-activities {
    text-align: center;
    padding: 3rem 2rem;
    color: #a0aec0;
}

.empty-state i,
.empty-activities i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    color: #e2e8f0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #718096;
}

.empty-state p,
.empty-activities p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 1024px) {
    .financial-summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .enhanced-financial-system {
        padding: 1rem;
    }
    
    .financial-header {
        flex-direction: column;
        text-align: center;
    }
    
    .invoices-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        min-width: auto;
    }
    
    .filters-group {
        justify-content: center;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        min-width: auto;
        flex: 1;
    }
    
    .invoices-table-container {
        overflow-x: auto;
    }
    
    .invoice-actions {
        flex-direction: column;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .financial-title {
        font-size: 2rem;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .financial-summary-cards {
        grid-template-columns: 1fr;
    }
    
    .summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .summary-change {
        position: static;
        margin-top: 0.5rem;
    }
}
