// Initialize SQLite database for Dental Lab Manager
let database = null;

const initDatabase = () => {
    return new Promise(async (resolve, reject) => {
        try {
            // Wait for SQL.js to be available
            if (typeof SQL === 'undefined') {
                // Try to initialize SQL.js if not already done
                if (typeof initSqlJs === 'function') {
                    const SQL_MODULE = await initSqlJs({
                        locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
                    });
                    window.SQL = SQL_MODULE;
                } else {
                    throw new Error('SQL.js library not available');
                }
            }

            const db = new SQL.Database();

            // Create users table
            db.run(`
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT NOT NULL CHECK(role IN ('admin', 'accountant', 'manager', 'employee')),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Create employees table
            db.run(`
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    job_title TEXT,
                    salary REAL DEFAULT 0,
                    commission_rate REAL DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Create doctors table
            db.run(`
                CREATE TABLE IF NOT EXISTS doctors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    clinic_address TEXT,
                    default_price_per_tooth REAL DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Create prostheses table
            db.run(`
                CREATE TABLE IF NOT EXISTS prostheses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id TEXT UNIQUE NOT NULL,
                    doctor_id INTEGER REFERENCES doctors(id),
                    patient_name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    color TEXT,
                    price_per_tooth REAL,
                    teeth_numbers TEXT,
                    teeth_count INTEGER,
                    total_price REAL,
                    delivery_date DATE,
                    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'in_progress', 'completed', 'delivered')),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Create expenses table
            db.run(`
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    description TEXT NOT NULL,
                    amount REAL NOT NULL,
                    category TEXT,
                    expense_date DATE DEFAULT CURRENT_DATE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Create payments table
            db.run(`
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prosthesis_id INTEGER REFERENCES prostheses(id),
                    amount REAL NOT NULL,
                    payment_date DATE DEFAULT CURRENT_DATE,
                    payment_method TEXT,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Create employee_commissions table
            db.run(`
                CREATE TABLE IF NOT EXISTS employee_commissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER REFERENCES employees(id),
                    prosthesis_id INTEGER REFERENCES prostheses(id),
                    commission_amount REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Insert default admin user if not exists
            const adminExists = db.exec("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
            if (adminExists[0] && adminExists[0].values[0][0] === 0) {
                db.run(`
                    INSERT INTO users (username, password, role)
                    VALUES ('admin', 'admin123', 'admin')
                `);
                console.log("Default admin user created: admin/admin123");
            }

            database = db;
            console.log("Database initialized successfully");
            resolve(db);
        } catch (error) {
            console.error("Database initialization error:", error);
            reject(error);
        }
    });
};

// Database utility functions
const getDatabase = () => database;

// User management functions
const authenticateUser = (username, password) => {
    if (!database) return null;

    const result = database.exec(`
        SELECT id, username, role FROM users
        WHERE username = ? AND password = ?
    `, [username, password]);

    return result[0] && result[0].values.length > 0 ? {
        id: result[0].values[0][0],
        username: result[0].values[0][1],
        role: result[0].values[0][2]
    } : null;
};

const addUser = (username, password, role) => {
    if (!database) return false;

    try {
        database.run(`
            INSERT INTO users (username, password, role)
            VALUES (?, ?, ?)
        `, [username, password, role]);
        return true;
    } catch (error) {
        console.error("Error adding user:", error);
        return false;
    }
};

// Doctor management functions
const addDoctor = (name, phone, clinicAddress, defaultPrice = 0) => {
    if (!database) return false;

    try {
        database.run(`
            INSERT INTO doctors (name, phone, clinic_address, default_price_per_tooth)
            VALUES (?, ?, ?, ?)
        `, [name, phone, clinicAddress, defaultPrice]);
        return true;
    } catch (error) {
        console.error("Error adding doctor:", error);
        return false;
    }
};

const getAllDoctors = () => {
    if (!database) return [];

    const result = database.exec(`
        SELECT id, name, phone, clinic_address, default_price_per_tooth
        FROM doctors ORDER BY name
    `);

    return result[0] ? result[0].values.map(row => ({
        id: row[0],
        name: row[1],
        phone: row[2],
        clinicAddress: row[3],
        defaultPrice: row[4]
    })) : [];
};

// Employee management functions
const addEmployee = (name, phone, jobTitle, salary, commissionRate) => {
    if (!database) return false;

    try {
        database.run(`
            INSERT INTO employees (name, phone, job_title, salary, commission_rate)
            VALUES (?, ?, ?, ?, ?)
        `, [name, phone, jobTitle, salary, commissionRate]);
        return true;
    } catch (error) {
        console.error("Error adding employee:", error);
        return false;
    }
};

const getAllEmployees = () => {
    if (!database) return [];

    const result = database.exec(`
        SELECT id, name, phone, job_title, salary, commission_rate
        FROM employees ORDER BY name
    `);

    return result[0] ? result[0].values.map(row => ({
        id: row[0],
        name: row[1],
        phone: row[2],
        jobTitle: row[3],
        salary: row[4],
        commissionRate: row[5]
    })) : [];
};

// Prosthesis management functions
const addProsthesis = (caseId, doctorId, patientName, type, color, pricePerTooth, teethNumbers, teethCount, deliveryDate) => {
    if (!database) return false;

    try {
        // حساب خاص للبرشل الجزئى: أول سن 150 جنيه، والباقي 90 جنيه لكل سن
        let totalPrice;
        if (type === 'partial_denture_brushel') {
            if (teethCount > 0) {
                totalPrice = 150; // أول سن
                if (teethCount > 1) {
                    totalPrice += (teethCount - 1) * 90; // باقي الأسنان
                }
            } else {
                totalPrice = 0;
            }
        } else {
            totalPrice = pricePerTooth * teethCount;
        }
        database.run(`
            INSERT INTO prostheses (case_id, doctor_id, patient_name, type, color, price_per_tooth, teeth_numbers, teeth_count, total_price, delivery_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [caseId, doctorId, patientName, type, color, pricePerTooth, teethNumbers, teethCount, totalPrice, deliveryDate]);
        return true;
    } catch (error) {
        console.error("Error adding prosthesis:", error);
        return false;
    }
};

const getAllProstheses = () => {
    if (!database) return [];

    const result = database.exec(`
        SELECT p.id, p.case_id, p.patient_name, p.type, p.color,
               p.price_per_tooth, p.teeth_numbers, p.teeth_count,
               p.total_price, p.delivery_date, p.status,
               d.name as doctor_name
        FROM prostheses p
        LEFT JOIN doctors d ON p.doctor_id = d.id
        ORDER BY p.created_at DESC
    `);

    return result[0] ? result[0].values.map(row => ({
        id: row[0],
        caseId: row[1],
        patientName: row[2],
        type: row[3],
        color: row[4],
        pricePerTooth: row[5],
        teethNumbers: row[6],
        teethCount: row[7],
        totalPrice: row[8],
        deliveryDate: row[9],
        status: row[10],
        doctorName: row[11]
    })) : [];
};

// Backup and restore functions
const exportData = () => {
    if (!database) return null;

    return database.export();
};

const importData = (data) => {
    try {
        database = new SQL.Database(data);
        return true;
    } catch (error) {
        console.error("Error importing data:", error);
        return false;
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initDatabase,
        getDatabase,
        authenticateUser,
        addUser,
        addDoctor,
        getAllDoctors,
        addEmployee,
        getAllEmployees,
        addProsthesis,
        getAllProstheses,
        exportData,
        importData
    };
}