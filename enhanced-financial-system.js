/**
 * النظام المالي المتكامل
 * Enhanced Financial System
 * 
 * المميزات:
 * - إدارة الفواتير والمدفوعات
 * - نظام المحاسبة المزدوجة
 * - تتبع الإيرادات والمصروفات
 * - التقارير المالية المفصلة
 * - إدارة الضرائب والخصومات
 * - نظام الائتمان والديون
 */

class EnhancedFinancialSystem {
    constructor() {
        this.invoices = [];
        this.payments = [];
        this.expenses = [];
        this.accounts = [];
        this.transactions = [];
        this.taxSettings = {};
        this.paymentMethods = [];
        this.loadData();
        this.initializeAccounts();
        this.initializePaymentMethods();
        this.initializeTaxSettings();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        this.invoices = data.invoices || [];
        this.payments = data.payments || [];
        this.expenses = data.expenses || [];
        this.accounts = data.accounts || [];
        this.transactions = data.transactions || [];
        this.taxSettings = data.taxSettings || {};
        this.paymentMethods = data.paymentMethods || [];
    }

    /**
     * حفظ البيانات
     */
    saveData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        data.invoices = this.invoices;
        data.payments = this.payments;
        data.expenses = this.expenses;
        data.accounts = this.accounts;
        data.transactions = this.transactions;
        data.taxSettings = this.taxSettings;
        data.paymentMethods = this.paymentMethods;
        localStorage.setItem('dental_lab_data', JSON.stringify(data));
    }

    /**
     * تهيئة الحسابات المحاسبية
     */
    initializeAccounts() {
        if (this.accounts.length === 0) {
            this.accounts = [
                // الأصول
                { id: 1, code: '1001', name: 'النقدية', type: 'asset', category: 'current_assets', balance: 0 },
                { id: 2, code: '1002', name: 'البنك', type: 'asset', category: 'current_assets', balance: 0 },
                { id: 3, code: '1003', name: 'العملاء', type: 'asset', category: 'current_assets', balance: 0 },
                { id: 4, code: '1101', name: 'المعدات', type: 'asset', category: 'fixed_assets', balance: 0 },
                { id: 5, code: '1102', name: 'الأثاث', type: 'asset', category: 'fixed_assets', balance: 0 },
                
                // الخصوم
                { id: 6, code: '2001', name: 'الموردين', type: 'liability', category: 'current_liabilities', balance: 0 },
                { id: 7, code: '2002', name: 'الرواتب المستحقة', type: 'liability', category: 'current_liabilities', balance: 0 },
                { id: 8, code: '2003', name: 'الضرائب المستحقة', type: 'liability', category: 'current_liabilities', balance: 0 },
                
                // حقوق الملكية
                { id: 9, code: '3001', name: 'رأس المال', type: 'equity', category: 'capital', balance: 0 },
                { id: 10, code: '3002', name: 'الأرباح المحتجزة', type: 'equity', category: 'retained_earnings', balance: 0 },
                
                // الإيرادات
                { id: 11, code: '4001', name: 'إيرادات التركيبات', type: 'revenue', category: 'operating_revenue', balance: 0 },
                { id: 12, code: '4002', name: 'إيرادات أخرى', type: 'revenue', category: 'other_revenue', balance: 0 },
                
                // المصروفات
                { id: 13, code: '5001', name: 'الرواتب', type: 'expense', category: 'operating_expenses', balance: 0 },
                { id: 14, code: '5002', name: 'الإيجار', type: 'expense', category: 'operating_expenses', balance: 0 },
                { id: 15, code: '5003', name: 'المواد الخام', type: 'expense', category: 'cost_of_goods', balance: 0 },
                { id: 16, code: '5004', name: 'الكهرباء والمياه', type: 'expense', category: 'operating_expenses', balance: 0 },
                { id: 17, code: '5005', name: 'مصروفات إدارية', type: 'expense', category: 'administrative_expenses', balance: 0 }
            ];
            this.saveData();
        }
    }

    /**
     * تهيئة طرق الدفع
     */
    initializePaymentMethods() {
        if (this.paymentMethods.length === 0) {
            this.paymentMethods = [
                { id: 1, name: 'نقدي', type: 'cash', isActive: true, accountId: 1 },
                { id: 2, name: 'تحويل بنكي', type: 'bank_transfer', isActive: true, accountId: 2 },
                { id: 3, name: 'شيك', type: 'check', isActive: true, accountId: 2 },
                { id: 4, name: 'فيزا/ماستركارد', type: 'credit_card', isActive: true, accountId: 2 },
                { id: 5, name: 'محفظة إلكترونية', type: 'e_wallet', isActive: true, accountId: 2 },
                { id: 6, name: 'آجل', type: 'credit', isActive: true, accountId: 3 }
            ];
            this.saveData();
        }
    }

    /**
     * تهيئة إعدادات الضرائب
     */
    initializeTaxSettings() {
        if (Object.keys(this.taxSettings).length === 0) {
            this.taxSettings = {
                vatRate: 14, // ضريبة القيمة المضافة 14%
                withholdingTaxRate: 1, // ضريبة الخصم والإضافة 1%
                isVatEnabled: true,
                isWithholdingTaxEnabled: false,
                taxNumber: '',
                companyName: 'معمل الأسنان المتطور',
                companyAddress: '',
                companyPhone: '',
                companyEmail: ''
            };
            this.saveData();
        }
    }

    /**
     * إنشاء HTML للنظام المالي
     */
    generateFinancialSystemHTML() {
        return `
            <div class="enhanced-financial-system">
                <!-- رأس النظام -->
                <div class="financial-header">
                    <div class="header-content">
                        <h1 class="financial-title">
                            <i class="fas fa-chart-line"></i>
                            النظام المالي المتكامل
                        </h1>
                        <p class="financial-subtitle">
                            إدارة شاملة للمالية والمحاسبة
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="financialSystem.showCreateInvoiceModal()">
                            <i class="fas fa-file-invoice"></i>
                            فاتورة جديدة
                        </button>
                        <button class="btn btn-success" onclick="financialSystem.showAddPaymentModal()">
                            <i class="fas fa-money-bill-wave"></i>
                            تسجيل دفعة
                        </button>
                        <button class="btn btn-warning" onclick="financialSystem.showAddExpenseModal()">
                            <i class="fas fa-receipt"></i>
                            إضافة مصروف
                        </button>
                        <button class="btn btn-info" onclick="financialSystem.generateFinancialReport()">
                            <i class="fas fa-chart-bar"></i>
                            التقارير المالية
                        </button>
                    </div>
                </div>

                <!-- الملخص المالي -->
                <div class="financial-summary">
                    ${this.generateFinancialSummary()}
                </div>

                <!-- تبويبات النظام المالي -->
                <div class="financial-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="financialSystem.switchFinancialTab('dashboard')">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </button>
                        <button class="tab-btn" onclick="financialSystem.switchFinancialTab('invoices')">
                            <i class="fas fa-file-invoice"></i>
                            الفواتير
                        </button>
                        <button class="tab-btn" onclick="financialSystem.switchFinancialTab('payments')">
                            <i class="fas fa-credit-card"></i>
                            المدفوعات
                        </button>
                        <button class="tab-btn" onclick="financialSystem.switchFinancialTab('expenses')">
                            <i class="fas fa-receipt"></i>
                            المصروفات
                        </button>
                        <button class="tab-btn" onclick="financialSystem.switchFinancialTab('accounts')">
                            <i class="fas fa-book"></i>
                            الحسابات
                        </button>
                        <button class="tab-btn" onclick="financialSystem.switchFinancialTab('reports')">
                            <i class="fas fa-chart-pie"></i>
                            التقارير
                        </button>
                        <button class="tab-btn" onclick="financialSystem.switchFinancialTab('settings')">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </button>
                    </div>

                    <!-- تبويب لوحة التحكم -->
                    <div id="dashboard-financial-tab" class="tab-content active">
                        ${this.generateDashboardTab()}
                    </div>

                    <!-- تبويب الفواتير -->
                    <div id="invoices-financial-tab" class="tab-content">
                        ${this.generateInvoicesTab()}
                    </div>

                    <!-- تبويب المدفوعات -->
                    <div id="payments-financial-tab" class="tab-content">
                        ${this.generatePaymentsTab()}
                    </div>

                    <!-- تبويب المصروفات -->
                    <div id="expenses-financial-tab" class="tab-content">
                        ${this.generateExpensesTab()}
                    </div>

                    <!-- تبويب الحسابات -->
                    <div id="accounts-financial-tab" class="tab-content">
                        ${this.generateAccountsTab()}
                    </div>

                    <!-- تبويب التقارير -->
                    <div id="reports-financial-tab" class="tab-content">
                        ${this.generateReportsTab()}
                    </div>

                    <!-- تبويب الإعدادات -->
                    <div id="settings-financial-tab" class="tab-content">
                        ${this.generateSettingsTab()}
                    </div>
                </div>

                <!-- النوافذ المنبثقة -->
                ${this.generateFinancialModals()}
            </div>
        `;
    }

    /**
     * إنشاء الملخص المالي
     */
    generateFinancialSummary() {
        const summary = this.calculateFinancialSummary();
        
        return `
            <div class="financial-summary-cards">
                <div class="summary-card revenue">
                    <div class="summary-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${summary.totalRevenue.toLocaleString()}</div>
                        <div class="summary-label">إجمالي الإيرادات (ج.م)</div>
                        <div class="summary-period">هذا الشهر</div>
                    </div>
                    <div class="summary-change ${summary.revenueChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${summary.revenueChange >= 0 ? 'up' : 'down'}"></i>
                        ${Math.abs(summary.revenueChange).toFixed(1)}%
                    </div>
                </div>

                <div class="summary-card expenses">
                    <div class="summary-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${summary.totalExpenses.toLocaleString()}</div>
                        <div class="summary-label">إجمالي المصروفات (ج.م)</div>
                        <div class="summary-period">هذا الشهر</div>
                    </div>
                    <div class="summary-change ${summary.expensesChange <= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${summary.expensesChange <= 0 ? 'down' : 'up'}"></i>
                        ${Math.abs(summary.expensesChange).toFixed(1)}%
                    </div>
                </div>

                <div class="summary-card profit">
                    <div class="summary-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${summary.netProfit.toLocaleString()}</div>
                        <div class="summary-label">صافي الربح (ج.م)</div>
                        <div class="summary-period">هذا الشهر</div>
                    </div>
                    <div class="summary-change ${summary.profitMargin >= 20 ? 'positive' : summary.profitMargin >= 10 ? 'neutral' : 'negative'}">
                        <i class="fas fa-percentage"></i>
                        ${summary.profitMargin.toFixed(1)}% هامش ربح
                    </div>
                </div>

                <div class="summary-card outstanding">
                    <div class="summary-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${summary.outstandingAmount.toLocaleString()}</div>
                        <div class="summary-label">مبالغ مستحقة (ج.م)</div>
                        <div class="summary-period">${summary.outstandingInvoices} فاتورة</div>
                    </div>
                    <div class="summary-change ${summary.outstandingAmount > summary.totalRevenue * 0.3 ? 'negative' : 'neutral'}">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${summary.outstandingAmount > summary.totalRevenue * 0.3 ? 'يحتاج متابعة' : 'ضمن المعدل'}
                    </div>
                </div>

                <div class="summary-card cash">
                    <div class="summary-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${summary.cashBalance.toLocaleString()}</div>
                        <div class="summary-label">الرصيد النقدي (ج.م)</div>
                        <div class="summary-period">الرصيد الحالي</div>
                    </div>
                    <div class="summary-change ${summary.cashBalance > summary.totalExpenses ? 'positive' : 'negative'}">
                        <i class="fas fa-${summary.cashBalance > summary.totalExpenses ? 'check-circle' : 'exclamation-circle'}"></i>
                        ${summary.cashBalance > summary.totalExpenses ? 'رصيد جيد' : 'رصيد منخفض'}
                    </div>
                </div>

                <div class="summary-card tax">
                    <div class="summary-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${summary.taxAmount.toLocaleString()}</div>
                        <div class="summary-label">الضرائب المستحقة (ج.م)</div>
                        <div class="summary-period">هذا الشهر</div>
                    </div>
                    <div class="summary-change neutral">
                        <i class="fas fa-percentage"></i>
                        ${this.taxSettings.vatRate}% ض.ق.م
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حساب الملخص المالي
     */
    calculateFinancialSummary() {
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        // الإيرادات
        const thisMonthRevenue = this.invoices
            .filter(inv => new Date(inv.date) >= thisMonth && new Date(inv.date) <= thisMonthEnd)
            .reduce((sum, inv) => sum + (inv.total || 0), 0);

        const lastMonthRevenue = this.invoices
            .filter(inv => {
                const date = new Date(inv.date);
                return date >= lastMonth && date < thisMonth;
            })
            .reduce((sum, inv) => sum + (inv.total || 0), 0);

        // المصروفات
        const thisMonthExpenses = this.expenses
            .filter(exp => new Date(exp.date) >= thisMonth && new Date(exp.date) <= thisMonthEnd)
            .reduce((sum, exp) => sum + (exp.amount || 0), 0);

        const lastMonthExpenses = this.expenses
            .filter(exp => {
                const date = new Date(exp.date);
                return date >= lastMonth && date < thisMonth;
            })
            .reduce((sum, exp) => sum + (exp.amount || 0), 0);

        // الحسابات
        const cashAccount = this.accounts.find(acc => acc.code === '1001');
        const bankAccount = this.accounts.find(acc => acc.code === '1002');
        const cashBalance = (cashAccount?.balance || 0) + (bankAccount?.balance || 0);

        // المبالغ المستحقة
        const outstandingInvoices = this.invoices.filter(inv => inv.status === 'pending' || inv.status === 'partial');
        const outstandingAmount = outstandingInvoices.reduce((sum, inv) => sum + (inv.remainingAmount || inv.total || 0), 0);

        // الضرائب
        const taxAmount = thisMonthRevenue * (this.taxSettings.vatRate / 100);

        // النسب
        const netProfit = thisMonthRevenue - thisMonthExpenses;
        const profitMargin = thisMonthRevenue > 0 ? (netProfit / thisMonthRevenue) * 100 : 0;
        const revenueChange = lastMonthRevenue > 0 ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;
        const expensesChange = lastMonthExpenses > 0 ? ((thisMonthExpenses - lastMonthExpenses) / lastMonthExpenses) * 100 : 0;

        return {
            totalRevenue: thisMonthRevenue,
            totalExpenses: thisMonthExpenses,
            netProfit: netProfit,
            profitMargin: profitMargin,
            outstandingAmount: outstandingAmount,
            outstandingInvoices: outstandingInvoices.length,
            cashBalance: cashBalance,
            taxAmount: taxAmount,
            revenueChange: revenueChange,
            expensesChange: expensesChange
        };
    }

    /**
     * إنشاء تبويب لوحة التحكم
     */
    generateDashboardTab() {
        return `
            <div class="dashboard-content">
                <!-- الرسوم البيانية المالية -->
                <div class="financial-charts">
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>الإيرادات مقابل المصروفات</h3>
                                <select id="revenue-chart-period">
                                    <option value="6">آخر 6 أشهر</option>
                                    <option value="12" selected>آخر 12 شهر</option>
                                </select>
                            </div>
                            <div class="chart-container">
                                <canvas id="revenue-expenses-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>توزيع المصروفات</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="expenses-distribution-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>التدفق النقدي</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="cash-flow-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>حالة الفواتير</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="invoices-status-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأنشطة المالية الحديثة -->
                <div class="recent-financial-activities">
                    <div class="activities-header">
                        <h3>الأنشطة المالية الحديثة</h3>
                        <button class="btn btn-sm btn-outline" onclick="financialSystem.viewAllActivities()">
                            عرض الكل
                        </button>
                    </div>
                    <div class="activities-list">
                        ${this.generateRecentActivities()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء تبويب الفواتير
     */
    generateInvoicesTab() {
        return `
            <div class="invoices-content">
                <!-- شريط البحث والفلاتر -->
                <div class="invoices-filters">
                    <div class="search-bar">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" id="invoices-search" placeholder="البحث في الفواتير..."
                                   onkeyup="financialSystem.filterInvoices()">
                        </div>
                    </div>
                    <div class="filters-group">
                        <select id="invoice-status-filter" onchange="financialSystem.filterInvoices()">
                            <option value="">جميع الحالات</option>
                            <option value="draft">مسودة</option>
                            <option value="sent">مرسلة</option>
                            <option value="paid">مدفوعة</option>
                            <option value="partial">مدفوعة جزئياً</option>
                            <option value="overdue">متأخرة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                        <input type="date" id="invoice-date-from" onchange="financialSystem.filterInvoices()"
                               title="من تاريخ">
                        <input type="date" id="invoice-date-to" onchange="financialSystem.filterInvoices()"
                               title="إلى تاريخ">
                        <select id="invoice-doctor-filter" onchange="financialSystem.filterInvoices()">
                            <option value="">جميع الأطباء</option>
                            ${this.getDoctorsOptions()}
                        </select>
                    </div>
                </div>

                <!-- قائمة الفواتير -->
                <div class="invoices-list">
                    ${this.generateInvoicesList()}
                </div>
            </div>
        `;
    }

    /**
     * إنشاء قائمة الفواتير
     */
    generateInvoicesList() {
        if (this.invoices.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-file-invoice"></i>
                    <h3>لا توجد فواتير</h3>
                    <p>ابدأ بإنشاء أول فاتورة</p>
                    <button class="btn btn-primary" onclick="financialSystem.showCreateInvoiceModal()">
                        <i class="fas fa-plus"></i>
                        إنشاء فاتورة جديدة
                    </button>
                </div>
            `;
        }

        return `
            <div class="invoices-table-container">
                <table class="invoices-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>العميل/الطبيب</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.invoices.map(invoice => this.generateInvoiceRow(invoice)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * إنشاء صف فاتورة
     */
    generateInvoiceRow(invoice) {
        const statusClass = this.getInvoiceStatusClass(invoice.status);
        const isOverdue = this.isInvoiceOverdue(invoice);

        return `
            <tr class="invoice-row ${statusClass} ${isOverdue ? 'overdue' : ''}" data-invoice-id="${invoice.id}">
                <td>
                    <div class="invoice-number">
                        <strong>${invoice.number || `INV-${invoice.id}`}</strong>
                        ${invoice.type === 'credit_note' ? '<span class="credit-note-badge">إشعار دائن</span>' : ''}
                    </div>
                </td>
                <td>${this.formatDate(invoice.date)}</td>
                <td>
                    <div class="client-info">
                        <div class="client-name">${invoice.clientName}</div>
                        <div class="client-type">${invoice.clientType === 'doctor' ? 'طبيب' : 'عميل'}</div>
                    </div>
                </td>
                <td>
                    <div class="amount-info">
                        <div class="total-amount">${invoice.total.toLocaleString()} ج.م</div>
                        ${invoice.remainingAmount > 0 ?
                            `<div class="remaining-amount">متبقي: ${invoice.remainingAmount.toLocaleString()} ج.م</div>` : ''
                        }
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">
                        ${this.getInvoiceStatusText(invoice.status)}
                    </span>
                    ${isOverdue ? '<span class="overdue-badge">متأخرة</span>' : ''}
                </td>
                <td>${this.formatDate(invoice.dueDate)}</td>
                <td>
                    <div class="invoice-actions">
                        <button class="btn btn-sm btn-primary" onclick="financialSystem.viewInvoice(${invoice.id})"
                                title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="financialSystem.editInvoice(${invoice.id})"
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="financialSystem.printInvoice(${invoice.id})"
                                title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        ${invoice.status !== 'paid' ?
                            `<button class="btn btn-sm btn-info" onclick="financialSystem.recordPayment(${invoice.id})"
                                     title="تسجيل دفعة">
                                <i class="fas fa-money-bill-wave"></i>
                            </button>` : ''
                        }
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline dropdown-toggle"
                                    onclick="financialSystem.toggleInvoiceDropdown(${invoice.id})">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu" id="invoice-dropdown-${invoice.id}">
                                <a href="#" onclick="financialSystem.duplicateInvoice(${invoice.id})">
                                    <i class="fas fa-copy"></i>
                                    نسخ
                                </a>
                                <a href="#" onclick="financialSystem.sendInvoice(${invoice.id})">
                                    <i class="fas fa-envelope"></i>
                                    إرسال
                                </a>
                                <a href="#" onclick="financialSystem.downloadInvoice(${invoice.id})">
                                    <i class="fas fa-download"></i>
                                    تحميل PDF
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" onclick="financialSystem.cancelInvoice(${invoice.id})" class="text-danger">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * إنشاء الأنشطة الحديثة
     */
    generateRecentActivities() {
        const activities = this.getRecentFinancialActivities();

        if (activities.length === 0) {
            return `
                <div class="empty-activities">
                    <i class="fas fa-history"></i>
                    <p>لا توجد أنشطة مالية حديثة</p>
                </div>
            `;
        }

        return activities.map(activity => `
            <div class="activity-item ${activity.type}">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                    <div class="activity-time">${this.formatTimeAgo(activity.timestamp)}</div>
                </div>
                <div class="activity-amount">
                    ${activity.amount ? `${activity.amount.toLocaleString()} ج.م` : ''}
                </div>
            </div>
        `).join('');
    }

    /**
     * الحصول على الأنشطة المالية الحديثة
     */
    getRecentFinancialActivities() {
        const activities = [];
        const last7Days = new Date();
        last7Days.setDate(last7Days.getDate() - 7);

        // الفواتير الحديثة
        this.invoices
            .filter(inv => new Date(inv.date) >= last7Days)
            .forEach(inv => {
                activities.push({
                    type: 'invoice',
                    icon: 'fas fa-file-invoice',
                    title: 'فاتورة جديدة',
                    description: `فاتورة ${inv.number || `INV-${inv.id}`} للعميل ${inv.clientName}`,
                    timestamp: inv.date,
                    amount: inv.total
                });
            });

        // المدفوعات الحديثة
        this.payments
            .filter(pay => new Date(pay.date) >= last7Days)
            .forEach(pay => {
                activities.push({
                    type: 'payment',
                    icon: 'fas fa-money-bill-wave',
                    title: 'دفعة مستلمة',
                    description: `دفعة من ${pay.clientName} بطريقة ${this.getPaymentMethodName(pay.methodId)}`,
                    timestamp: pay.date,
                    amount: pay.amount
                });
            });

        // المصروفات الحديثة
        this.expenses
            .filter(exp => new Date(exp.date) >= last7Days)
            .forEach(exp => {
                activities.push({
                    type: 'expense',
                    icon: 'fas fa-receipt',
                    title: 'مصروف جديد',
                    description: `${exp.description} - ${this.getAccountName(exp.accountId)}`,
                    timestamp: exp.date,
                    amount: exp.amount
                });
            });

        // ترتيب حسب التاريخ (الأحدث أولاً)
        return activities
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, 10);
    }

    /**
     * الحصول على فئة حالة الفاتورة
     */
    getInvoiceStatusClass(status) {
        const statusClasses = {
            'draft': 'status-draft',
            'sent': 'status-sent',
            'paid': 'status-paid',
            'partial': 'status-partial',
            'overdue': 'status-overdue',
            'cancelled': 'status-cancelled'
        };
        return statusClasses[status] || 'status-unknown';
    }

    /**
     * الحصول على نص حالة الفاتورة
     */
    getInvoiceStatusText(status) {
        const statusTexts = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'partial': 'مدفوعة جزئياً',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * التحقق من تأخر الفاتورة
     */
    isInvoiceOverdue(invoice) {
        if (invoice.status === 'paid' || invoice.status === 'cancelled') {
            return false;
        }
        const now = new Date();
        const dueDate = new Date(invoice.dueDate);
        return dueDate < now;
    }

    /**
     * الحصول على خيارات الأطباء
     */
    getDoctorsOptions() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const doctors = data.doctors || [];

        return doctors.map(doctor =>
            `<option value="${doctor.id}">${doctor.name}</option>`
        ).join('');
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    getPaymentMethodName(methodId) {
        const method = this.paymentMethods.find(m => m.id === methodId);
        return method ? method.name : 'غير محدد';
    }

    /**
     * الحصول على اسم الحساب
     */
    getAccountName(accountId) {
        const account = this.accounts.find(a => a.id === accountId);
        return account ? account.name : 'غير محدد';
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * تنسيق الوقت المنقضي
     */
    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'منذ لحظات';
        if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
        if (diffInSeconds < 2592000) return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;

        return this.formatDate(dateString);
    }

    /**
     * تبديل التبويبات المالية
     */
    switchFinancialTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.financial-tabs .tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.financial-tabs .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-financial-tab`).classList.add('active');
        event.target.classList.add('active');
    }

    /**
     * فلترة الفواتير
     */
    filterInvoices() {
        // سيتم تنفيذها لاحقاً
        console.log('فلترة الفواتير');
    }

    /**
     * عرض نافذة إنشاء فاتورة
     */
    showCreateInvoiceModal() {
        alert('ميزة إنشاء فاتورة ستكون متاحة قريباً');
    }

    /**
     * عرض نافذة إضافة دفعة
     */
    showAddPaymentModal() {
        alert('ميزة إضافة دفعة ستكون متاحة قريباً');
    }

    /**
     * عرض نافذة إضافة مصروف
     */
    showAddExpenseModal() {
        alert('ميزة إضافة مصروف ستكون متاحة قريباً');
    }

    /**
     * إنشاء تقرير مالي
     */
    generateFinancialReport() {
        alert('ميزة التقارير المالية ستكون متاحة قريباً');
    }
}

// إنشاء مثيل عام من النظام المالي
const financialSystem = new EnhancedFinancialSystem();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedFinancialSystem, financialSystem };
}
