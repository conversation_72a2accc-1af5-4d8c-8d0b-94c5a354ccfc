<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام معمل الأسنان المتطور - Enhanced Dental Lab System</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- مكتبة الرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- الأنماط الأساسية -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f8fafc;
            color: #2d3748;
            direction: rtl;
            overflow-x: hidden;
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        .loading-text {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            animation: loading 3s ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes loading {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .main-container {
            display: none;
            min-height: 100vh;
        }

        .main-container.active {
            display: block;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .sidebar-logo {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            display: block;
            padding: 1rem 2rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .nav-item:hover,
        .nav-item.active {
            background: #f7fafc;
            color: #667eea;
            border-right-color: #667eea;
        }

        .nav-item i {
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-right: 280px;
            transition: margin-right 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        .top-bar {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #4a5568;
            cursor: pointer;
        }

        .top-bar-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.25rem;
            color: #4a5568;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .notification-btn:hover {
            background: #f7fafc;
        }

        .notification-badge {
            position: absolute;
            top: 0;
            left: 0;
            background: #f56565;
            color: white;
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: background 0.3s ease;
        }

        .user-menu:hover {
            background: #f7fafc;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .content-area {
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .module-content {
            display: none;
        }

        .module-content.active {
            display: block;
        }

        /* التجاوب مع الشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .menu-toggle {
                display: block;
            }

            .top-bar {
                padding: 1rem;
            }

            .content-area {
                padding: 1rem;
            }
        }
    </style>
    
    <!-- ملفات CSS للوحدات -->
    <link rel="stylesheet" href="enhanced-auth-system.css">
    <link rel="stylesheet" href="enhanced-dashboard.css">
    <link rel="stylesheet" href="enhanced-doctors-management.css">
    <link rel="stylesheet" href="enhanced-employees-management.css">
    <link rel="stylesheet" href="enhanced-prostheses-system.css">
    <link rel="stylesheet" href="enhanced-financial-system.css">
    <link rel="stylesheet" href="enhanced-reports-system.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-logo">
            <i class="fas fa-tooth"></i>
        </div>
        <div class="loading-text">نظام معمل الأسنان المتطور</div>
        <div class="loading-progress">
            <div class="loading-bar"></div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="main-container" id="mainContainer">
        <!-- الشريط الجانبي -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-tooth"></i>
                </div>
                <div class="sidebar-title">معمل الأسنان المتطور</div>
            </div>
            <div class="sidebar-nav">
                <a href="#" class="nav-item active" onclick="showModule('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="#" class="nav-item" onclick="showModule('doctors')">
                    <i class="fas fa-user-md"></i>
                    إدارة الأطباء
                </a>
                <a href="#" class="nav-item" onclick="showModule('employees')">
                    <i class="fas fa-users"></i>
                    إدارة الموظفين
                </a>
                <a href="#" class="nav-item" onclick="showModule('prostheses')">
                    <i class="fas fa-tooth"></i>
                    نظام التركيبات
                </a>
                <a href="#" class="nav-item" onclick="showModule('financial')">
                    <i class="fas fa-chart-line"></i>
                    النظام المالي
                </a>
                <a href="#" class="nav-item" onclick="showModule('reports')">
                    <i class="fas fa-chart-bar"></i>
                    التقارير والإحصائيات
                </a>
                <a href="#" class="nav-item" onclick="showModule('settings')">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
                <a href="#" class="nav-item" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content" id="mainContent">
            <!-- الشريط العلوي -->
            <header class="top-bar">
                <button class="menu-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="top-bar-actions">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="user-menu" onclick="toggleUserMenu()">
                        <div class="user-avatar">م</div>
                        <div class="user-info">
                            <div style="font-weight: 600;">مدير النظام</div>
                            <div style="font-size: 0.8rem; color: #718096;"><EMAIL></div>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
            </header>

            <!-- منطقة المحتوى -->
            <div class="content-area">
                <!-- وحدة لوحة التحكم -->
                <div id="dashboard-module" class="module-content active">
                    <!-- سيتم تحميل محتوى لوحة التحكم هنا -->
                </div>

                <!-- وحدة إدارة الأطباء -->
                <div id="doctors-module" class="module-content">
                    <!-- سيتم تحميل محتوى إدارة الأطباء هنا -->
                </div>

                <!-- وحدة إدارة الموظفين -->
                <div id="employees-module" class="module-content">
                    <!-- سيتم تحميل محتوى إدارة الموظفين هنا -->
                </div>

                <!-- وحدة نظام التركيبات -->
                <div id="prostheses-module" class="module-content">
                    <!-- سيتم تحميل محتوى نظام التركيبات هنا -->
                </div>

                <!-- وحدة النظام المالي -->
                <div id="financial-module" class="module-content">
                    <!-- سيتم تحميل محتوى النظام المالي هنا -->
                </div>

                <!-- وحدة التقارير والإحصائيات -->
                <div id="reports-module" class="module-content">
                    <!-- سيتم تحميل محتوى التقارير هنا -->
                </div>

                <!-- وحدة الإعدادات -->
                <div id="settings-module" class="module-content">
                    <div style="text-align: center; padding: 4rem; color: #a0aec0;">
                        <i class="fas fa-cog" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h3>وحدة الإعدادات</h3>
                        <p>ستكون متاحة في التحديث القادم</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- ملفات JavaScript للوحدات -->
    <script src="enhanced-auth-system.js"></script>
    <script src="enhanced-dashboard.js"></script>
    <script src="enhanced-doctors-management.js"></script>
    <script src="enhanced-employees-management.js"></script>
    <script src="enhanced-prostheses-system.js"></script>
    <script src="enhanced-financial-system.js"></script>
    <script src="enhanced-reports-system.js"></script>

    <!-- JavaScript الرئيسي -->
    <script>
        // متغيرات عامة
        let currentModule = 'dashboard';
        let sidebarCollapsed = false;

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            // محاكاة تحميل النظام
            setTimeout(() => {
                document.getElementById('loadingScreen').classList.add('hidden');
                document.getElementById('mainContainer').classList.add('active');
                
                // تحميل الوحدة الافتراضية
                loadModule('dashboard');
            }, 3000);
        });

        // تبديل الشريط الجانبي
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebarCollapsed = !sidebarCollapsed;
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('active');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        }

        // عرض وحدة معينة
        function showModule(moduleName) {
            // إخفاء جميع الوحدات
            document.querySelectorAll('.module-content').forEach(module => {
                module.classList.remove('active');
            });
            
            // إزالة التفعيل من جميع عناصر التنقل
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // تفعيل الوحدة المحددة
            document.getElementById(`${moduleName}-module`).classList.add('active');
            event.target.classList.add('active');
            
            // تحميل محتوى الوحدة
            loadModule(moduleName);
            
            // إغلاق الشريط الجانبي في الشاشات الصغيرة
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('active');
            }
            
            currentModule = moduleName;
        }

        // تحميل محتوى الوحدة
        function loadModule(moduleName) {
            const moduleContainer = document.getElementById(`${moduleName}-module`);
            
            switch (moduleName) {
                case 'dashboard':
                    if (typeof enhancedDashboard !== 'undefined') {
                        moduleContainer.innerHTML = enhancedDashboard.generateDashboardHTML();
                        enhancedDashboard.initializeCharts();
                    }
                    break;
                    
                case 'doctors':
                    if (typeof doctorsManager !== 'undefined') {
                        moduleContainer.innerHTML = doctorsManager.generateDoctorsManagementHTML();
                    }
                    break;
                    
                case 'employees':
                    if (typeof employeesManager !== 'undefined') {
                        moduleContainer.innerHTML = employeesManager.generateEmployeesManagementHTML();
                    }
                    break;
                    
                case 'prostheses':
                    if (typeof prosthesesSystem !== 'undefined') {
                        moduleContainer.innerHTML = prosthesesSystem.generateProsthesesSystemHTML();
                    }
                    break;
                    
                case 'financial':
                    if (typeof financialSystem !== 'undefined') {
                        moduleContainer.innerHTML = financialSystem.generateFinancialSystemHTML();
                    }
                    break;
                    
                case 'reports':
                    if (typeof reportsSystem !== 'undefined') {
                        moduleContainer.innerHTML = reportsSystem.generateReportsSystemHTML();
                    }
                    break;
            }
        }

        // تبديل قائمة المستخدم
        function toggleUserMenu() {
            // سيتم تنفيذها لاحقاً
            console.log('تبديل قائمة المستخدم');
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // إعادة تحميل الصفحة أو إعادة توجيه لصفحة تسجيل الدخول
                location.reload();
            }
        }

        // التعامل مع تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('sidebar').classList.remove('active');
            }
        });

        // إغلاق الشريط الجانبي عند النقر خارجه في الشاشات الصغيرة
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const menuToggle = document.querySelector('.menu-toggle');
                
                if (!sidebar.contains(event.target) && !menuToggle.contains(event.target)) {
                    sidebar.classList.remove('active');
                }
            }
        });
    </script>
</body>
</html>
