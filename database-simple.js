// Simple localStorage-based database for Dental Lab Manager
// This is a fallback solution that doesn't require SQL.js

let database = null;

// Initialize simple database using localStorage
const initDatabase = () => {
    return new Promise((resolve, reject) => {
        try {
            console.log('Initializing simple localStorage database...');
            
            // Initialize data structure if not exists
            if (!localStorage.getItem('dental_lab_data')) {
                const initialData = {
                    users: [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            role: 'admin',
                            fullName: 'مدير النظام',
                            email: '<EMAIL>',
                            phone: '',
                            isActive: true,
                            lastLogin: null,
                            created_at: new Date().toISOString()
                        }
                    ],
                    employees: [],
                    doctors: [],
                    prostheses: [],
                    expenses: [],
                    payments: [],
                    employee_commissions: [],
                    lab_settings: {
                        labName: 'معمل الأسنان المتخصص',
                        address: 'العنوان',
                        phones: ['***********', '03-5750974', '***********'],
                        email: '',
                        website: '',
                        logo: '',
                        created_at: new Date().toISOString()
                    },
                    nextId: {
                        users: 2,
                        employees: 1,
                        doctors: 1,
                        prostheses: 1,
                        expenses: 1,
                        payments: 1,
                        employee_commissions: 1
                    }
                };
                
                localStorage.setItem('dental_lab_data', JSON.stringify(initialData));
                console.log('Default admin user created: admin/admin123');
            }
            
            database = JSON.parse(localStorage.getItem('dental_lab_data'));
            console.log('Simple database initialized successfully');
            resolve(database);
        } catch (error) {
            console.error('Database initialization error:', error);
            reject(error);
        }
    });
};

// Save data to localStorage
const saveData = () => {
    try {
        localStorage.setItem('dental_lab_data', JSON.stringify(database));
        return true;
    } catch (error) {
        console.error('Error saving data:', error);
        return false;
    }
};

// Get database
const getDatabase = () => database;

// User management functions
const authenticateUser = (username, password) => {
    if (!database) return null;

    const user = database.users.find(u =>
        u.username === username &&
        u.password === password &&
        u.isActive === true
    );

    if (user) {
        // Update last login
        user.lastLogin = new Date().toISOString();
        saveData();

        return {
            id: user.id,
            username: user.username,
            role: user.role,
            fullName: user.fullName,
            email: user.email
        };
    }

    return null;
};

const addUser = (userData) => {
    if (!database) return false;

    try {
        // Check if user already exists
        if (database.users.find(u => u.username === userData.username)) {
            return { success: false, message: 'اسم المستخدم موجود بالفعل' };
        }

        // Check if email already exists
        if (userData.email && database.users.find(u => u.email === userData.email)) {
            return { success: false, message: 'البريد الإلكتروني موجود بالفعل' };
        }

        const newUser = {
            id: database.nextId.users++,
            username: userData.username,
            password: userData.password,
            role: userData.role,
            fullName: userData.fullName || '',
            email: userData.email || '',
            phone: userData.phone || '',
            isActive: true,
            lastLogin: null,
            created_at: new Date().toISOString()
        };

        database.users.push(newUser);
        const saved = saveData();

        return saved ?
            { success: true, message: 'تم إضافة المستخدم بنجاح' } :
            { success: false, message: 'فشل في حفظ البيانات' };

    } catch (error) {
        console.error("Error adding user:", error);
        return { success: false, message: 'حدث خطأ أثناء إضافة المستخدم' };
    }
};

const getAllUsers = () => {
    if (!database) return [];
    return database.users.map(user => ({
        id: user.id,
        username: user.username,
        role: user.role,
        fullName: user.fullName,
        email: user.email,
        phone: user.phone,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        created_at: user.created_at
    }));
};

const updateUser = (id, userData) => {
    if (!database) return { success: false, message: 'قاعدة البيانات غير متاحة' };

    try {
        const userIndex = database.users.findIndex(u => u.id === id);
        if (userIndex === -1) {
            return { success: false, message: 'المستخدم غير موجود' };
        }

        // Check if username is taken by another user
        const existingUser = database.users.find(u => u.username === userData.username && u.id !== id);
        if (existingUser) {
            return { success: false, message: 'اسم المستخدم موجود بالفعل' };
        }

        // Check if email is taken by another user
        if (userData.email) {
            const existingEmail = database.users.find(u => u.email === userData.email && u.id !== id);
            if (existingEmail) {
                return { success: false, message: 'البريد الإلكتروني موجود بالفعل' };
            }
        }

        // Update user data
        database.users[userIndex] = {
            ...database.users[userIndex],
            username: userData.username,
            role: userData.role,
            fullName: userData.fullName || '',
            email: userData.email || '',
            phone: userData.phone || '',
            isActive: userData.isActive !== undefined ? userData.isActive : database.users[userIndex].isActive,
            updated_at: new Date().toISOString()
        };

        // Update password only if provided
        if (userData.password && userData.password.trim() !== '') {
            database.users[userIndex].password = userData.password;
        }

        const saved = saveData();
        return saved ?
            { success: true, message: 'تم تحديث بيانات المستخدم بنجاح' } :
            { success: false, message: 'فشل في حفظ البيانات' };

    } catch (error) {
        console.error("Error updating user:", error);
        return { success: false, message: 'حدث خطأ أثناء تحديث المستخدم' };
    }
};

const deleteUser = (id) => {
    if (!database) return { success: false, message: 'قاعدة البيانات غير متاحة' };

    try {
        const userIndex = database.users.findIndex(u => u.id === id);
        if (userIndex === -1) {
            return { success: false, message: 'المستخدم غير موجود' };
        }

        // Prevent deleting the last admin
        const user = database.users[userIndex];
        if (user.role === 'admin') {
            const adminCount = database.users.filter(u => u.role === 'admin' && u.isActive).length;
            if (adminCount <= 1) {
                return { success: false, message: 'لا يمكن حذف آخر مدير في النظام' };
            }
        }

        database.users.splice(userIndex, 1);
        const saved = saveData();

        return saved ?
            { success: true, message: 'تم حذف المستخدم بنجاح' } :
            { success: false, message: 'فشل في حفظ البيانات' };

    } catch (error) {
        console.error("Error deleting user:", error);
        return { success: false, message: 'حدث خطأ أثناء حذف المستخدم' };
    }
};

const toggleUserStatus = (id) => {
    if (!database) return { success: false, message: 'قاعدة البيانات غير متاحة' };

    try {
        const userIndex = database.users.findIndex(u => u.id === id);
        if (userIndex === -1) {
            return { success: false, message: 'المستخدم غير موجود' };
        }

        const user = database.users[userIndex];

        // Prevent deactivating the last admin
        if (user.role === 'admin' && user.isActive) {
            const activeAdminCount = database.users.filter(u => u.role === 'admin' && u.isActive).length;
            if (activeAdminCount <= 1) {
                return { success: false, message: 'لا يمكن إلغاء تفعيل آخر مدير نشط في النظام' };
            }
        }

        database.users[userIndex].isActive = !database.users[userIndex].isActive;
        database.users[userIndex].updated_at = new Date().toISOString();

        const saved = saveData();
        const status = database.users[userIndex].isActive ? 'تفعيل' : 'إلغاء تفعيل';

        return saved ?
            { success: true, message: `تم ${status} المستخدم بنجاح` } :
            { success: false, message: 'فشل في حفظ البيانات' };

    } catch (error) {
        console.error("Error toggling user status:", error);
        return { success: false, message: 'حدث خطأ أثناء تغيير حالة المستخدم' };
    }
};

// Doctor management functions
const addDoctor = (name, phone, phone2, clinicAddress) => {
    if (!database) return false;

    try {
        const newDoctor = {
            id: database.nextId.doctors++,
            name,
            phone,
            phone2: phone2 || '',
            clinicAddress,
            created_at: new Date().toISOString()
        };

        database.doctors.push(newDoctor);
        return saveData();
    } catch (error) {
        console.error("Error adding doctor:", error);
        return false;
    }
};

const getAllDoctors = () => {
    if (!database) return [];
    return database.doctors.map(doctor => ({
        id: doctor.id,
        name: doctor.name,
        phone: doctor.phone,
        phone2: doctor.phone2 || '',
        clinicAddress: doctor.clinicAddress,
        priceList: doctor.priceList || {}
    }));
};

const updateDoctor = (id, doctorData) => {
    if (!database) return false;

    try {
        const doctorIndex = database.doctors.findIndex(doc => doc.id === id);
        if (doctorIndex === -1) return false;

        database.doctors[doctorIndex] = {
            ...database.doctors[doctorIndex],
            name: doctorData.name,
            phone: doctorData.phone,
            phone2: doctorData.phone2 || '',
            clinicAddress: doctorData.clinicAddress,
            priceList: doctorData.priceList || database.doctors[doctorIndex].priceList || {},
            updated_at: new Date().toISOString()
        };

        return saveData();
    } catch (error) {
        console.error("Error updating doctor:", error);
        return false;
    }
};

const removeDoctor = (id) => {
    if (!database) return false;

    try {
        const doctorIndex = database.doctors.findIndex(doc => doc.id === id);
        if (doctorIndex === -1) return false;

        // Check if doctor has prostheses
        const hasProstheses = database.prostheses.some(p => p.doctorId === id);
        if (hasProstheses) {
            return false; // Cannot delete doctor with prostheses
        }

        database.doctors.splice(doctorIndex, 1);
        return saveData();
    } catch (error) {
        console.error("Error removing doctor:", error);
        return false;
    }
};

// Employee management functions
const addEmployee = (name, phone, idCard, jobTitle, salary, commissionRate, porcelainRate, zirconRate, orthodonticRate, removableRate, deductionDays, notes) => {
    if (!database) return false;

    try {
        const newEmployee = {
            id: database.nextId.employees++,
            name,
            phone,
            idCard: idCard || '',
            jobTitle,
            salary,
            commissionRate,
            porcelainRate: porcelainRate || 0,
            zirconRate: zirconRate || 0,
            orthodonticRate: orthodonticRate || 0,
            removableRate: removableRate || 0,
            deductionDays: deductionDays || 0,
            notes: notes || '',
            created_at: new Date().toISOString()
        };

        database.employees.push(newEmployee);
        return saveData();
    } catch (error) {
        console.error("Error adding employee:", error);
        return false;
    }
};

const getAllEmployees = () => {
    if (!database) return [];
    return database.employees.map(employee => ({
        id: employee.id,
        name: employee.name,
        phone: employee.phone,
        idCard: employee.idCard,
        jobTitle: employee.jobTitle,
        salary: employee.salary,
        commissionRate: employee.commissionRate,
        porcelainRate: employee.porcelainRate || 0,
        zirconRate: employee.zirconRate || 0,
        orthodonticRate: employee.orthodonticRate || 0,
        removableRate: employee.removableRate || 0,
        deductionDays: employee.deductionDays || 0,
        notes: employee.notes || ''
    }));
};

const updateEmployee = (id, employeeData) => {
    if (!database) return false;

    try {
        const employeeIndex = database.employees.findIndex(emp => emp.id === id);
        if (employeeIndex === -1) return false;

        database.employees[employeeIndex] = {
            ...database.employees[employeeIndex],
            name: employeeData.name,
            phone: employeeData.phone,
            idCard: employeeData.idCard || '',
            jobTitle: employeeData.jobTitle,
            salary: employeeData.salary,
            commissionRate: employeeData.commissionRate,
            porcelainRate: employeeData.porcelainRate || 0,
            zirconRate: employeeData.zirconRate || 0,
            orthodonticRate: employeeData.orthodonticRate || 0,
            removableRate: employeeData.removableRate || 0,
            deductionDays: employeeData.deductionDays || 0,
            notes: employeeData.notes || '',
            updated_at: new Date().toISOString()
        };

        return saveData();
    } catch (error) {
        console.error("Error updating employee:", error);
        return false;
    }
};

const removeEmployee = (id) => {
    if (!database) return false;

    try {
        const employeeIndex = database.employees.findIndex(emp => emp.id === id);
        if (employeeIndex === -1) return false;

        database.employees.splice(employeeIndex, 1);

        // Also remove related commissions
        database.employee_commissions = database.employee_commissions.filter(
            comm => comm.employee_id !== id
        );

        return saveData();
    } catch (error) {
        console.error("Error removing employee:", error);
        return false;
    }
};

// Prosthesis management functions
const addProsthesis = (caseId, doctorId, patientName, type, color, pricePerTooth, teethNumbers, teethCount, deliveryDate) => {
    if (!database) return false;
    
    try {
        // Check if case ID already exists
        if (database.prostheses.find(p => p.caseId === caseId)) {
            return false;
        }
        
        // حساب خاص للبرشل الجزئى: أول سن 150 جنيه، والباقي 90 جنيه لكل سن
        let totalPrice;
        if (type === 'partial_denture_brushel') {
            if (teethCount > 0) {
                totalPrice = 150; // أول سن
                if (teethCount > 1) {
                    totalPrice += (teethCount - 1) * 90; // باقي الأسنان
                }
            } else {
                totalPrice = 0;
            }
        } else {
            totalPrice = pricePerTooth * teethCount;
        }
        const newProsthesis = {
            id: database.nextId.prostheses++,
            caseId,
            doctorId,
            patientName,
            type,
            color,
            pricePerTooth,
            teethNumbers,
            teethCount,
            totalPrice,
            deliveryDate,
            status: 'pending',
            created_at: new Date().toISOString()
        };
        
        database.prostheses.push(newProsthesis);
        return saveData();
    } catch (error) {
        console.error("Error adding prosthesis:", error);
        return false;
    }
};

const getAllProstheses = () => {
    if (!database) return [];
    return database.prostheses.map(prosthesis => {
        // Use flexible matching to handle type mismatches
        const doctor = database.doctors.find(d => d.id == prosthesis.doctorId);
        return {
            id: prosthesis.id,
            caseId: prosthesis.caseId,
            doctorId: prosthesis.doctorId, // Include doctorId in the returned object
            patientName: prosthesis.patientName,
            type: prosthesis.type,
            color: prosthesis.color,
            pricePerTooth: prosthesis.pricePerTooth,
            teethNumbers: prosthesis.teethNumbers,
            teethCount: prosthesis.teethCount,
            totalPrice: prosthesis.totalPrice,
            deliveryDate: prosthesis.deliveryDate,
            status: prosthesis.status,
            doctorName: doctor ? doctor.name : 'غير محدد'
        };
    });
};

const updateProsthesis = (id, prosthesisData) => {
    if (!database) return false;

    try {
        const prosthesisIndex = database.prostheses.findIndex(p => p.id === id);
        if (prosthesisIndex === -1) return false;

        const totalPrice = prosthesisData.pricePerTooth * prosthesisData.teethCount;

        database.prostheses[prosthesisIndex] = {
            ...database.prostheses[prosthesisIndex],
            caseId: prosthesisData.caseId,
            doctorId: prosthesisData.doctorId,
            patientName: prosthesisData.patientName,
            type: prosthesisData.type,
            color: prosthesisData.color,
            pricePerTooth: prosthesisData.pricePerTooth,
            teethNumbers: prosthesisData.teethNumbers,
            teethCount: prosthesisData.teethCount,
            totalPrice: totalPrice,
            deliveryDate: prosthesisData.deliveryDate,
            status: prosthesisData.status || database.prostheses[prosthesisIndex].status,
            updated_at: new Date().toISOString()
        };

        return saveData();
    } catch (error) {
        console.error("Error updating prosthesis:", error);
        return false;
    }
};

const removeProsthesis = (id) => {
    if (!database) return false;

    try {
        const prosthesisIndex = database.prostheses.findIndex(p => p.id === id);
        if (prosthesisIndex === -1) return false;

        database.prostheses.splice(prosthesisIndex, 1);

        // Also remove related payments and commissions
        database.payments = database.payments.filter(p => p.prosthesisId !== id);
        database.employee_commissions = database.employee_commissions.filter(c => c.prosthesis_id !== id);

        return saveData();
    } catch (error) {
        console.error("Error removing prosthesis:", error);
        return false;
    }
};

const updateProsthesisStatus = (id, status) => {
    if (!database) return false;

    try {
        const prosthesisIndex = database.prostheses.findIndex(p => p.id === id);
        if (prosthesisIndex === -1) return false;

        database.prostheses[prosthesisIndex].status = status;
        database.prostheses[prosthesisIndex].updated_at = new Date().toISOString();

        return saveData();
    } catch (error) {
        console.error("Error updating prosthesis status:", error);
        return false;
    }
};

// Expense management functions
const addExpense = (description, amount, category, expenseDate) => {
    if (!database) return false;

    try {
        const newExpense = {
            id: database.nextId.expenses++,
            description,
            amount,
            category,
            expenseDate: expenseDate || new Date().toISOString().split('T')[0],
            created_at: new Date().toISOString()
        };

        database.expenses.push(newExpense);
        return saveData();
    } catch (error) {
        console.error("Error adding expense:", error);
        return false;
    }
};

const getAllExpenses = () => {
    if (!database) return [];
    return database.expenses.map(expense => ({
        id: expense.id,
        description: expense.description,
        amount: expense.amount,
        category: expense.category,
        expenseDate: expense.expenseDate,
        created_at: expense.created_at
    }));
};

const updateExpense = (id, expenseData) => {
    if (!database) return false;

    try {
        const expenseIndex = database.expenses.findIndex(exp => exp.id === id);
        if (expenseIndex === -1) return false;

        database.expenses[expenseIndex] = {
            ...database.expenses[expenseIndex],
            description: expenseData.description,
            amount: expenseData.amount,
            category: expenseData.category,
            expenseDate: expenseData.expenseDate,
            updated_at: new Date().toISOString()
        };

        return saveData();
    } catch (error) {
        console.error("Error updating expense:", error);
        return false;
    }
};

const removeExpense = (id) => {
    if (!database) return false;

    try {
        const expenseIndex = database.expenses.findIndex(exp => exp.id === id);
        if (expenseIndex === -1) return false;

        database.expenses.splice(expenseIndex, 1);
        return saveData();
    } catch (error) {
        console.error("Error removing expense:", error);
        return false;
    }
};

// Payment management functions
const addPayment = (prosthesisId, amount, paymentDate, paymentMethod, notes) => {
    if (!database) return false;

    try {
        const newPayment = {
            id: database.nextId.payments++,
            prosthesisId,
            amount,
            paymentDate: paymentDate || new Date().toISOString().split('T')[0],
            paymentMethod,
            notes,
            created_at: new Date().toISOString()
        };

        database.payments.push(newPayment);
        return saveData();
    } catch (error) {
        console.error("Error adding payment:", error);
        return false;
    }
};

const getAllPayments = () => {
    if (!database) return [];
    return database.payments.map(payment => {
        const prosthesis = database.prostheses.find(p => p.id === payment.prosthesisId);
        return {
            id: payment.id,
            prosthesisId: payment.prosthesisId,
            amount: payment.amount,
            paymentDate: payment.paymentDate,
            paymentMethod: payment.paymentMethod,
            notes: payment.notes,
            prosthesisCaseId: prosthesis ? prosthesis.caseId : 'غير محدد',
            patientName: prosthesis ? prosthesis.patientName : 'غير محدد'
        };
    });
};

// Lab settings functions
const getLabSettings = () => {
    if (!database || !database.lab_settings) {
        return {
            labName: 'معمل الأسنان المتخصص',
            address: 'العنوان',
            phones: ['***********', '03-5750974', '***********'],
            email: '',
            website: '',
            logo: ''
        };
    }
    return database.lab_settings;
};

const updateLabSettings = (settings) => {
    if (!database) return { success: false, message: 'قاعدة البيانات غير متاحة' };

    try {
        database.lab_settings = {
            ...database.lab_settings,
            labName: settings.labName || 'معمل الأسنان المتخصص',
            address: settings.address || '',
            phones: settings.phones || [],
            email: settings.email || '',
            website: settings.website || '',
            logo: settings.logo || '',
            updated_at: new Date().toISOString()
        };

        const saved = saveData();
        return saved ?
            { success: true, message: 'تم تحديث إعدادات المعمل بنجاح' } :
            { success: false, message: 'فشل في حفظ البيانات' };

    } catch (error) {
        console.error("Error updating lab settings:", error);
        return { success: false, message: 'حدث خطأ أثناء تحديث الإعدادات' };
    }
};

// Backup and restore functions
const exportData = () => {
    if (!database) return null;
    return JSON.stringify(database, null, 2);
};

const importData = (jsonData) => {
    try {
        const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
        localStorage.setItem('dental_lab_data', JSON.stringify(data));
        database = data;
        return true;
    } catch (error) {
        console.error("Error importing data:", error);
        return false;
    }
};

// Clear all data (for testing)
const clearAllData = () => {
    localStorage.removeItem('dental_lab_data');
    database = null;
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initDatabase,
        getDatabase,
        authenticateUser,
        addUser,
        getAllUsers,
        updateUser,
        deleteUser,
        toggleUserStatus,
        addDoctor,
        getAllDoctors,
        updateDoctor,
        removeDoctor,
        addEmployee,
        getAllEmployees,
        updateEmployee,
        removeEmployee,
        addProsthesis,
        getAllProstheses,
        updateProsthesis,
        removeProsthesis,
        updateProsthesisStatus,
        addExpense,
        getAllExpenses,
        updateExpense,
        removeExpense,
        addPayment,
        getAllPayments,
        getLabSettings,
        updateLabSettings,
        exportData,
        importData,
        clearAllData
    };
}
