/**
 * نظام إدارة الموظفين المحسن
 * Enhanced Employees Management System
 * 
 * المميزات:
 * - إدارة شاملة للموظفين
 * - نظام الرواتب والعمولات
 * - تتبع الحضور والانصراف
 * - نظام التقييمات والمكافآت
 * - إدارة الإجازات والعطل
 * - تقارير الأداء المفصلة
 */

class EnhancedEmployeesManagement {
    constructor() {
        this.employees = [];
        this.attendance = [];
        this.salaries = [];
        this.evaluations = [];
        this.leaves = [];
        this.departments = [];
        this.loadData();
        this.initializeDepartments();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        this.employees = data.employees || [];
        this.attendance = data.attendance || [];
        this.salaries = data.salaries || [];
        this.evaluations = data.evaluations || [];
        this.leaves = data.leaves || [];
        this.departments = data.departments || [];
    }

    /**
     * حفظ البيانات
     */
    saveData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        data.employees = this.employees;
        data.attendance = this.attendance;
        data.salaries = this.salaries;
        data.evaluations = this.evaluations;
        data.leaves = this.leaves;
        data.departments = this.departments;
        localStorage.setItem('dental_lab_data', JSON.stringify(data));
    }

    /**
     * تهيئة الأقسام الافتراضية
     */
    initializeDepartments() {
        if (this.departments.length === 0) {
            this.departments = [
                { id: 1, name: 'الإدارة', description: 'الإدارة العامة والتخطيط' },
                { id: 2, name: 'الإنتاج', description: 'تصنيع التركيبات السنية' },
                { id: 3, name: 'الجودة', description: 'مراقبة الجودة والفحص' },
                { id: 4, name: 'المبيعات', description: 'التسويق وخدمة العملاء' },
                { id: 5, name: 'المحاسبة', description: 'الشؤون المالية والمحاسبة' },
                { id: 6, name: 'الصيانة', description: 'صيانة المعدات والأجهزة' }
            ];
            this.saveData();
        }
    }

    /**
     * إنشاء HTML لوحدة إدارة الموظفين
     */
    generateEmployeesManagementHTML() {
        return `
            <div class="enhanced-employees-management">
                <!-- رأس الوحدة -->
                <div class="module-header">
                    <div class="header-content">
                        <h1 class="module-title">
                            <i class="fas fa-users"></i>
                            إدارة الموظفين الشاملة
                        </h1>
                        <p class="module-subtitle">
                            نظام متكامل لإدارة الموظفين والرواتب والحضور
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="employeesManager.showAddEmployeeModal()">
                            <i class="fas fa-user-plus"></i>
                            إضافة موظف جديد
                        </button>
                        <button class="btn btn-info" onclick="employeesManager.showAttendanceModal()">
                            <i class="fas fa-clock"></i>
                            تسجيل الحضور
                        </button>
                        <button class="btn btn-success" onclick="employeesManager.showSalariesModal()">
                            <i class="fas fa-money-bill-wave"></i>
                            إدارة الرواتب
                        </button>
                        <button class="btn btn-secondary" onclick="employeesManager.exportEmployeesData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="employees-stats">
                    ${this.generateEmployeesStats()}
                </div>

                <!-- تبويبات الوحدة -->
                <div class="module-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="employeesManager.switchMainTab('employees')">
                            <i class="fas fa-users"></i>
                            الموظفين
                        </button>
                        <button class="tab-btn" onclick="employeesManager.switchMainTab('attendance')">
                            <i class="fas fa-clock"></i>
                            الحضور والانصراف
                        </button>
                        <button class="tab-btn" onclick="employeesManager.switchMainTab('salaries')">
                            <i class="fas fa-money-bill-wave"></i>
                            الرواتب والعمولات
                        </button>
                        <button class="tab-btn" onclick="employeesManager.switchMainTab('evaluations')">
                            <i class="fas fa-star"></i>
                            التقييمات
                        </button>
                        <button class="tab-btn" onclick="employeesManager.switchMainTab('leaves')">
                            <i class="fas fa-calendar-times"></i>
                            الإجازات
                        </button>
                        <button class="tab-btn" onclick="employeesManager.switchMainTab('reports')">
                            <i class="fas fa-chart-bar"></i>
                            التقارير
                        </button>
                    </div>

                    <!-- تبويب الموظفين -->
                    <div id="employees-main-tab" class="tab-content active">
                        ${this.generateEmployeesTab()}
                    </div>

                    <!-- تبويب الحضور والانصراف -->
                    <div id="attendance-main-tab" class="tab-content">
                        ${this.generateAttendanceTab()}
                    </div>

                    <!-- تبويب الرواتب -->
                    <div id="salaries-main-tab" class="tab-content">
                        ${this.generateSalariesTab()}
                    </div>

                    <!-- تبويب التقييمات -->
                    <div id="evaluations-main-tab" class="tab-content">
                        ${this.generateEvaluationsTab()}
                    </div>

                    <!-- تبويب الإجازات -->
                    <div id="leaves-main-tab" class="tab-content">
                        ${this.generateLeavesTab()}
                    </div>

                    <!-- تبويب التقارير -->
                    <div id="reports-main-tab" class="tab-content">
                        ${this.generateReportsTab()}
                    </div>
                </div>

                <!-- النوافذ المنبثقة -->
                ${this.generateModals()}
            </div>
        `;
    }

    /**
     * إنشاء إحصائيات الموظفين
     */
    generateEmployeesStats() {
        const totalEmployees = this.employees.length;
        const activeEmployees = this.employees.filter(e => e.status === 'active').length;
        const presentToday = this.getTodayPresentCount();
        const totalSalaries = this.calculateTotalSalaries();
        const avgSalary = totalEmployees > 0 ? totalSalaries / totalEmployees : 0;

        return `
            <div class="stats-cards">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${totalEmployees}</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-user-check"></i>
                            ${activeEmployees} نشط
                        </div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${presentToday}</div>
                        <div class="stat-label">الحضور اليوم</div>
                        <div class="stat-change ${presentToday >= activeEmployees * 0.8 ? 'positive' : 'negative'}">
                            <i class="fas fa-percentage"></i>
                            ${activeEmployees > 0 ? Math.round((presentToday / activeEmployees) * 100) : 0}% من الموظفين
                        </div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${totalSalaries.toLocaleString()}</div>
                        <div class="stat-label">إجمالي الرواتب (ج.م)</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-calculator"></i>
                            متوسط ${avgSalary.toLocaleString()} ج.م
                        </div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.getPendingLeavesCount()}</div>
                        <div class="stat-label">طلبات إجازة معلقة</div>
                        <div class="stat-change ${this.getPendingLeavesCount() > 5 ? 'negative' : 'neutral'}">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${this.getPendingLeavesCount() > 5 ? 'يحتاج مراجعة' : 'ضمن المعدل'}
                        </div>
                    </div>
                </div>

                <div class="stat-card danger">
                    <div class="stat-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.getAbsentTodayCount()}</div>
                        <div class="stat-label">غائب اليوم</div>
                        <div class="stat-change ${this.getAbsentTodayCount() > activeEmployees * 0.2 ? 'negative' : 'positive'}">
                            <i class="fas fa-exclamation-circle"></i>
                            ${this.getAbsentTodayCount() > activeEmployees * 0.2 ? 'معدل عالي' : 'معدل طبيعي'}
                        </div>
                    </div>
                </div>

                <div class="stat-card secondary">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${this.getAveragePerformance().toFixed(1)}%</div>
                        <div class="stat-label">متوسط الأداء</div>
                        <div class="stat-change ${this.getAveragePerformance() >= 80 ? 'positive' : this.getAveragePerformance() >= 60 ? 'neutral' : 'negative'}">
                            <i class="fas fa-${this.getAveragePerformance() >= 80 ? 'thumbs-up' : this.getAveragePerformance() >= 60 ? 'minus' : 'thumbs-down'}"></i>
                            ${this.getAveragePerformance() >= 80 ? 'ممتاز' : this.getAveragePerformance() >= 60 ? 'جيد' : 'يحتاج تحسين'}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء تبويب الموظفين
     */
    generateEmployeesTab() {
        return `
            <div class="employees-tab-content">
                <!-- شريط البحث والفلاتر -->
                <div class="search-filters-section">
                    <div class="search-bar">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" id="employees-search" placeholder="البحث عن موظف..." 
                                   onkeyup="employeesManager.filterEmployees()">
                        </div>
                    </div>
                    <div class="filters-group">
                        <select id="department-filter" onchange="employeesManager.filterEmployees()">
                            <option value="">جميع الأقسام</option>
                            ${this.departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('')}
                        </select>
                        <select id="position-filter" onchange="employeesManager.filterEmployees()">
                            <option value="">جميع المناصب</option>
                            <option value="manager">مدير</option>
                            <option value="supervisor">مشرف</option>
                            <option value="technician">فني</option>
                            <option value="assistant">مساعد</option>
                            <option value="accountant">محاسب</option>
                        </select>
                        <select id="employee-status-filter" onchange="employeesManager.filterEmployees()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                    </div>
                </div>

                <!-- قائمة الموظفين -->
                <div class="employees-grid" id="employees-grid">
                    ${this.generateEmployeesGrid()}
                </div>
            </div>
        `;
    }

    /**
     * إنشاء شبكة الموظفين
     */
    generateEmployeesGrid() {
        if (this.employees.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>لا يوجد موظفون مسجلون</h3>
                    <p>ابدأ بإضافة أول موظف للنظام</p>
                    <button class="btn btn-primary" onclick="employeesManager.showAddEmployeeModal()">
                        <i class="fas fa-user-plus"></i>
                        إضافة موظف جديد
                    </button>
                </div>
            `;
        }

        return this.employees.map(employee => this.generateEmployeeCard(employee)).join('');
    }

    /**
     * إنشاء بطاقة موظف
     */
    generateEmployeeCard(employee) {
        const department = this.departments.find(d => d.id === employee.departmentId);
        const attendance = this.getTodayAttendance(employee.id);
        const salary = this.getEmployeeSalary(employee.id);
        const performance = this.getEmployeePerformance(employee.id);
        const statusClass = employee.status || 'active';

        return `
            <div class="employee-card ${statusClass}" data-employee-id="${employee.id}">
                <div class="employee-header">
                    <div class="employee-avatar">
                        ${employee.avatar ? 
                            `<img src="${employee.avatar}" alt="${employee.name}">` : 
                            `<i class="fas fa-user"></i>`
                        }
                        <div class="status-indicator ${statusClass}"></div>
                    </div>
                    <div class="employee-basic-info">
                        <h3 class="employee-name">${employee.name}</h3>
                        <p class="employee-position">${this.getPositionText(employee.position)}</p>
                        <p class="employee-department">${department ? department.name : 'غير محدد'}</p>
                        <div class="employee-id">ID: ${employee.employeeId || employee.id}</div>
                    </div>
                    <div class="employee-status">
                        <span class="status-badge ${statusClass}">${this.getEmployeeStatusText(employee.status)}</span>
                    </div>
                </div>

                <div class="employee-stats">
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span class="stat-value">${attendance ? 'حاضر' : 'غائب'}</span>
                        <span class="stat-label">اليوم</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-money-bill"></i>
                        <span class="stat-value">${salary.toLocaleString()}</span>
                        <span class="stat-label">الراتب</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-chart-line"></i>
                        <span class="stat-value">${performance.toFixed(1)}%</span>
                        <span class="stat-label">الأداء</span>
                    </div>
                </div>

                <div class="employee-contact">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>${employee.phone || 'غير محدد'}</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span>${employee.email || 'غير محدد'}</span>
                    </div>
                </div>

                <div class="employee-actions">
                    <button class="btn btn-sm btn-primary" onclick="employeesManager.viewEmployeeDetails(${employee.id})">
                        <i class="fas fa-eye"></i>
                        التفاصيل
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="employeesManager.editEmployee(${employee.id})">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-info" onclick="employeesManager.manageEmployeeSalary(${employee.id})">
                        <i class="fas fa-dollar-sign"></i>
                        الراتب
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline dropdown-toggle" onclick="employeesManager.toggleDropdown(${employee.id})">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="dropdown-menu" id="employee-dropdown-${employee.id}">
                            <a href="#" onclick="employeesManager.recordAttendance(${employee.id})">
                                <i class="fas fa-clock"></i>
                                تسجيل حضور
                            </a>
                            <a href="#" onclick="employeesManager.addEvaluation(${employee.id})">
                                <i class="fas fa-star"></i>
                                إضافة تقييم
                            </a>
                            <a href="#" onclick="employeesManager.manageLeaves(${employee.id})">
                                <i class="fas fa-calendar-times"></i>
                                إدارة الإجازات
                            </a>
                            <a href="#" onclick="employeesManager.generateEmployeeReport(${employee.id})">
                                <i class="fas fa-chart-bar"></i>
                                تقرير الأداء
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" onclick="employeesManager.toggleEmployeeStatus(${employee.id})" 
                               class="${employee.status === 'active' ? 'text-warning' : 'text-success'}">
                                <i class="fas fa-${employee.status === 'active' ? 'pause' : 'play'}"></i>
                                ${employee.status === 'active' ? 'إيقاف مؤقت' : 'تفعيل'}
                            </a>
                            <a href="#" onclick="employeesManager.deleteEmployee(${employee.id})" class="text-danger">
                                <i class="fas fa-trash"></i>
                                حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * الحصول على عدد الحاضرين اليوم
     */
    getTodayPresentCount() {
        const today = new Date().toISOString().split('T')[0];
        return this.attendance.filter(a =>
            a.date === today && a.status === 'present'
        ).length;
    }

    /**
     * حساب إجمالي الرواتب
     */
    calculateTotalSalaries() {
        return this.employees.reduce((total, employee) => {
            return total + this.getEmployeeSalary(employee.id);
        }, 0);
    }

    /**
     * الحصول على عدد طلبات الإجازة المعلقة
     */
    getPendingLeavesCount() {
        return this.leaves.filter(leave => leave.status === 'pending').length;
    }

    /**
     * الحصول على عدد الغائبين اليوم
     */
    getAbsentTodayCount() {
        const today = new Date().toISOString().split('T')[0];
        const presentEmployees = this.attendance
            .filter(a => a.date === today && a.status === 'present')
            .map(a => a.employeeId);

        return this.employees.filter(e =>
            e.status === 'active' && !presentEmployees.includes(e.id)
        ).length;
    }

    /**
     * الحصول على متوسط الأداء
     */
    getAveragePerformance() {
        if (this.employees.length === 0) return 0;

        const totalPerformance = this.employees.reduce((total, employee) => {
            return total + this.getEmployeePerformance(employee.id);
        }, 0);

        return totalPerformance / this.employees.length;
    }

    /**
     * الحصول على حضور اليوم للموظف
     */
    getTodayAttendance(employeeId) {
        const today = new Date().toISOString().split('T')[0];
        return this.attendance.find(a =>
            a.employeeId === employeeId && a.date === today
        );
    }

    /**
     * الحصول على راتب الموظف
     */
    getEmployeeSalary(employeeId) {
        const employee = this.employees.find(e => e.id === employeeId);
        return employee ? (employee.baseSalary || 0) : 0;
    }

    /**
     * الحصول على أداء الموظف
     */
    getEmployeePerformance(employeeId) {
        const evaluations = this.evaluations.filter(e => e.employeeId === employeeId);
        if (evaluations.length === 0) return 75; // أداء افتراضي

        const totalScore = evaluations.reduce((sum, eval) => sum + eval.score, 0);
        return totalScore / evaluations.length;
    }

    /**
     * الحصول على نص المنصب
     */
    getPositionText(position) {
        const positions = {
            'manager': 'مدير',
            'supervisor': 'مشرف',
            'technician': 'فني',
            'assistant': 'مساعد',
            'accountant': 'محاسب',
            'secretary': 'سكرتير',
            'cleaner': 'عامل نظافة'
        };
        return positions[position] || position || 'غير محدد';
    }

    /**
     * الحصول على نص حالة الموظف
     */
    getEmployeeStatusText(status) {
        const statusTexts = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'suspended': 'معلق',
            'on_leave': 'في إجازة',
            'terminated': 'منتهي الخدمة'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * فلترة الموظفين
     */
    filterEmployees() {
        const searchTerm = document.getElementById('employees-search').value.toLowerCase();
        const departmentFilter = document.getElementById('department-filter').value;
        const positionFilter = document.getElementById('position-filter').value;
        const statusFilter = document.getElementById('employee-status-filter').value;

        const filteredEmployees = this.employees.filter(employee => {
            const matchesSearch = employee.name.toLowerCase().includes(searchTerm) ||
                                (employee.phone && employee.phone.includes(searchTerm)) ||
                                (employee.email && employee.email.toLowerCase().includes(searchTerm));

            const matchesDepartment = !departmentFilter || employee.departmentId == departmentFilter;
            const matchesPosition = !positionFilter || employee.position === positionFilter;
            const matchesStatus = !statusFilter || employee.status === statusFilter;

            return matchesSearch && matchesDepartment && matchesPosition && matchesStatus;
        });

        this.displayFilteredEmployees(filteredEmployees);
    }

    /**
     * عرض الموظفين المفلترين
     */
    displayFilteredEmployees(employees) {
        const grid = document.getElementById('employees-grid');

        if (employees.length === 0) {
            grid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد نتائج</h3>
                    <p>لم يتم العثور على موظفين تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = employees.map(employee => this.generateEmployeeCard(employee)).join('');
    }

    /**
     * تبديل التبويبات الرئيسية
     */
    switchMainTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.module-tabs .tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.module-tabs .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-main-tab`).classList.add('active');
        event.target.classList.add('active');
    }

    /**
     * عرض نافذة إضافة موظف
     */
    showAddEmployeeModal() {
        // سيتم تنفيذها لاحقاً
        alert('ميزة إضافة موظف ستكون متاحة قريباً');
    }

    /**
     * عرض نافذة الحضور
     */
    showAttendanceModal() {
        // سيتم تنفيذها لاحقاً
        alert('ميزة تسجيل الحضور ستكون متاحة قريباً');
    }

    /**
     * عرض نافذة الرواتب
     */
    showSalariesModal() {
        // سيتم تنفيذها لاحقاً
        alert('ميزة إدارة الرواتب ستكون متاحة قريباً');
    }

    /**
     * تصدير بيانات الموظفين
     */
    exportEmployeesData() {
        const exportData = {
            employees: this.employees,
            attendance: this.attendance,
            salaries: this.salaries,
            evaluations: this.evaluations,
            leaves: this.leaves,
            departments: this.departments,
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `employees-data-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }
}

// إنشاء مثيل عام من إدارة الموظفين
const employeesManager = new EnhancedEmployeesManagement();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedEmployeesManagement, employeesManager };
}
