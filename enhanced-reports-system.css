/**
 * أنماط نظام التقارير والإحصائيات المتقدم
 * Enhanced Reports System Styles
 */

/* الحاوي الرئيسي */
.enhanced-reports-system {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    direction: rtl;
}

/* رأس النظام */
.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    flex-wrap: wrap;
    gap: 1rem;
}

.reports-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reports-title i {
    color: #667eea;
}

.reports-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* مؤشرات الأداء الرئيسية */
.kpis-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.kpi-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.kpi-controls select {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
}

.kpis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* بطاقة مؤشر الأداء */
.kpi-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--kpi-color);
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* ألوان مؤشرات الأداء */
.kpi-card.excellent { --kpi-color: #48bb78; }
.kpi-card.good { --kpi-color: #4299e1; }
.kpi-card.average { --kpi-color: #ed8936; }
.kpi-card.below-average { --kpi-color: #f56565; }
.kpi-card.poor { --kpi-color: #e53e3e; }

.kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.kpi-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--kpi-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.kpi-trend {
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.kpi-trend.positive {
    background: #c6f6d5;
    color: #38a169;
}

.kpi-trend.negative {
    background: #fed7d7;
    color: #e53e3e;
}

.kpi-content {
    margin-bottom: 1rem;
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.kpi-name {
    font-size: 1rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.25rem;
}

.kpi-target {
    font-size: 0.875rem;
    color: #718096;
}

.kpi-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.excellent { background: linear-gradient(90deg, #48bb78, #38a169); }
.progress-fill.good { background: linear-gradient(90deg, #4299e1, #3182ce); }
.progress-fill.average { background: linear-gradient(90deg, #ed8936, #dd6b20); }
.progress-fill.below-average { background: linear-gradient(90deg, #f56565, #e53e3e); }
.progress-fill.poor { background: linear-gradient(90deg, #e53e3e, #c53030); }

.progress-text {
    font-size: 0.75rem;
    color: #718096;
    text-align: center;
}

.kpi-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* التبويبات */
.reports-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e2e8f0;
    background: #f7fafc;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #718096;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 140px;
    justify-content: center;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.tab-btn:hover {
    color: #4a5568;
    background: rgba(255, 255, 255, 0.5);
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* الرسوم البيانية الرئيسية */
.main-charts {
    margin-bottom: 2rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1.5rem;
}

.chart-card {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
}

.chart-card.large {
    grid-row: span 2;
}

.chart-card.medium {
    /* الحجم الافتراضي */
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chart-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.chart-controls select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.875rem;
}

.chart-container {
    height: 300px;
    position: relative;
}

.chart-card.large .chart-container {
    height: 400px;
}

/* الملخص التنفيذي */
.executive-summary {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.summary-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.summary-period {
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.summary-icon.revenue { background: #48bb78; }
.summary-icon.production { background: #4299e1; }
.summary-icon.quality { background: #ed8936; }
.summary-icon.efficiency { background: #9f7aea; }

.summary-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
}

.summary-content p {
    font-size: 0.875rem;
    color: #718096;
    margin: 0 0 0.25rem 0;
}

.growth.positive { color: #38a169; }
.growth.negative { color: #e53e3e; }

/* التنبيهات والتوصيات */
.insights-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.insights-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.insights-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.insights-list {
    max-height: 400px;
    overflow-y: auto;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.insight-item:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.insight-item.warning .insight-icon {
    background: #fed7d7;
    color: #e53e3e;
}

.insight-item.success .insight-icon {
    background: #c6f6d5;
    color: #38a169;
}

.insight-item.info .insight-icon {
    background: #bee3f8;
    color: #3182ce;
}

.insight-content {
    flex: 1;
}

.insight-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.insight-description {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.insight-action {
    margin-top: 0.5rem;
}

.insight-priority {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.insight-priority.high {
    background: #fed7d7;
    color: #e53e3e;
}

.insight-priority.medium {
    background: #fef5e7;
    color: #d69e2e;
}

.insight-priority.low {
    background: #e2e8f0;
    color: #718096;
}

.empty-insights {
    text-align: center;
    padding: 3rem 2rem;
    color: #a0aec0;
}

.empty-insights i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    color: #e2e8f0;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    white-space: nowrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-1px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 1024px) {
    .kpis-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-card.large {
        grid-row: span 1;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .enhanced-reports-system {
        padding: 1rem;
    }
    
    .reports-header {
        flex-direction: column;
        text-align: center;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .kpi-controls {
        justify-content: center;
    }
    
    .kpis-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        min-width: auto;
        flex: 1;
    }
    
    .summary-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .insights-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .reports-title {
        font-size: 2rem;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .kpi-card {
        padding: 1rem;
    }
    
    .kpi-value {
        font-size: 1.5rem;
    }
    
    .summary-item {
        flex-direction: column;
        text-align: center;
    }
    
    .insight-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .insight-priority {
        align-self: center;
    }
}
