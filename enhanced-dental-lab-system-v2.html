<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-navbutton-color" content="#667eea">
    <meta name="apple-mobile-web-app-title" content="معمل الأسنان المتطور">
    
    <title>نظام معمل الأسنان المتطور - الإصدار المحسن للكمبيوتر والموبايل</title>
    
    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- الأيقونات المحسنة -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous">
    
    <!-- مكتبة الرسوم البيانية المحسنة -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- الأنماط المحسنة -->
    <link rel="stylesheet" href="responsive-mobile-enhancements.css">
    <link rel="stylesheet" href="adaptive-design-system.css">
    <link rel="stylesheet" href="enhanced-auth-system.css">
    <link rel="stylesheet" href="enhanced-dashboard.css">
    <link rel="stylesheet" href="enhanced-doctors-management.css">
    <link rel="stylesheet" href="enhanced-employees-management.css">
    <link rel="stylesheet" href="enhanced-prostheses-system.css">
    <link rel="stylesheet" href="enhanced-financial-system.css">
    <link rel="stylesheet" href="enhanced-reports-system.css">
    
    <!-- أنماط إضافية للنظام المحسن -->
    <style>
        /* تحسينات إضافية للأداء */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }
        
        /* تحسين التحميل */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
        }
        
        .loading-logo {
            width: clamp(80px, 15vw, 120px);
            height: clamp(80px, 15vw, 120px);
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: clamp(2rem, 6vw, 3rem);
            color: #667eea;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .loading-text {
            color: white;
            font-size: clamp(1rem, 4vw, 1.5rem);
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
            padding: 0 1rem;
        }
        
        .loading-progress {
            width: min(300px, 80vw);
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        
        .loading-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            animation: loading 3s ease-in-out;
        }
        
        .loading-status {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
            text-align: center;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes loading {
            0% { width: 0%; }
            25% { width: 30%; }
            50% { width: 60%; }
            75% { width: 85%; }
            100% { width: 100%; }
        }
        
        /* تحسين الشريط الجانبي */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 100%;
            max-width: min(320px, 85vw);
            height: 100vh;
            background: var(--bg-primary);
            box-shadow: var(--shadow-2xl);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .sidebar.active {
            transform: translateX(0);
        }
        
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            backdrop-filter: blur(2px);
        }
        
        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        /* تحسين الشريط العلوي */
        .top-bar {
            position: sticky;
            top: 0;
            background: var(--bg-primary);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
            min-height: 60px;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
        }
        
        /* تحسين المحتوى الرئيسي */
        .main-content {
            transition: margin-right var(--transition-normal);
            min-height: 100vh;
            background: var(--bg-secondary);
        }
        
        .content-area {
            padding: var(--spacing-lg);
            min-height: calc(100vh - 60px);
        }
        
        /* تحسين الوحدات */
        .module-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .module-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* تحسين الإشعارات */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: 50%;
            transition: all var(--transition-fast);
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .notification-btn:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--color-error);
            color: white;
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
            border-radius: var(--radius-full);
            min-width: 18px;
            text-align: center;
            font-weight: 600;
            line-height: 1;
        }
        
        /* تحسين قائمة المستخدم */
        .user-menu {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            cursor: pointer;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            transition: background var(--transition-fast);
            min-height: 44px;
        }
        
        .user-menu:hover {
            background: var(--bg-secondary);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            flex-shrink: 0;
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        
        .user-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }
        
        .user-email {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        
        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
            }
            
            .content-area {
                padding: var(--spacing-md);
            }
            
            .user-info {
                display: none;
            }
            
            .top-bar {
                padding: var(--spacing-sm) var(--spacing-md);
            }
            
            .loading-logo {
                margin-bottom: 1.5rem;
            }
            
            .loading-text {
                margin-bottom: 0.75rem;
            }
        }
        
        /* تحسينات للأجهزة الكبيرة */
        @media (min-width: 1024px) {
            .sidebar {
                position: fixed;
                transform: translateX(0);
                width: 280px;
                max-width: 280px;
            }
            
            .sidebar.collapsed {
                transform: translateX(100%);
            }
            
            .main-content {
                margin-right: 280px;
            }
            
            .main-content.expanded {
                margin-right: 0;
            }
            
            .sidebar-overlay {
                display: none;
            }
        }
        
        /* تحسينات إمكانية الوصول */
        @media (prefers-reduced-motion: reduce) {
            .loading-logo {
                animation: none;
            }
            
            .loading-bar {
                animation: none;
                width: 100%;
            }
            
            .module-content {
                animation: none;
            }
        }
        
        /* أنماط الصفحات الافتراضية */
        .welcome-card {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            color: white;
            padding: 2rem;
            border-radius: var(--radius-lg);
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .empty-state {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            border: 2px dashed var(--border-color);
        }

        .stats-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-md);
            transition: transform var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stats-primary .stats-icon { background: var(--color-primary); }
        .stats-success .stats-icon { background: var(--color-success); }
        .stats-info .stats-icon { background: var(--color-info); }
        .stats-warning .stats-icon { background: var(--color-warning); }

        .stats-content {
            flex: 1;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .stats-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        /* تحسينات للطباعة */
        @media print {
            .sidebar,
            .top-bar,
            .loading-screen {
                display: none !important;
            }

            .main-content {
                margin-right: 0 !important;
            }

            .content-area {
                padding: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل المحسنة -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-logo">
            <i class="fas fa-tooth"></i>
        </div>
        <div class="loading-text">نظام معمل الأسنان المتطور</div>
        <div class="loading-progress">
            <div class="loading-bar"></div>
        </div>
        <div class="loading-status" id="loadingStatus">جاري التحميل...</div>
    </div>

    <!-- طبقة تراكب الشريط الجانبي -->
    <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>

    <!-- الحاوي الرئيسي -->
    <div class="main-container" id="mainContainer">
        <!-- الشريط الجانبي المحسن -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-tooth"></i>
                </div>
                <div class="sidebar-title">معمل الأسنان المتطور</div>
                <div class="sidebar-subtitle">الإصدار المحسن</div>
            </div>
            <div class="sidebar-nav">
                <a href="#" class="nav-item active" data-action="show-module" data-module="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="#" class="nav-item" data-action="show-module" data-module="doctors">
                    <i class="fas fa-user-md"></i>
                    إدارة الأطباء
                </a>
                <a href="#" class="nav-item" data-action="show-module" data-module="employees">
                    <i class="fas fa-users"></i>
                    إدارة الموظفين
                </a>
                <a href="#" class="nav-item" data-action="show-module" data-module="prostheses">
                    <i class="fas fa-tooth"></i>
                    نظام التركيبات
                </a>
                <a href="#" class="nav-item" data-action="show-module" data-module="financial">
                    <i class="fas fa-chart-line"></i>
                    النظام المالي
                </a>
                <a href="#" class="nav-item" data-action="show-module" data-module="reports">
                    <i class="fas fa-chart-bar"></i>
                    التقارير والإحصائيات
                </a>
                <a href="#" class="nav-item" data-action="show-module" data-module="settings">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
                <div class="nav-divider"></div>
                <a href="#" class="nav-item" data-action="logout">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>

        <!-- المحتوى الرئيسي المحسن -->
        <main class="main-content" id="mainContent">
            <!-- الشريط العلوي المحسن -->
            <header class="top-bar">
                <div class="top-bar-start">
                    <button class="menu-toggle" data-action="toggle-sidebar" aria-label="فتح/إغلاق القائمة">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb d-none d-md-flex">
                        <span class="breadcrumb-item active" id="currentModuleName">لوحة التحكم</span>
                    </div>
                </div>
                
                <div class="top-bar-actions">
                    <button class="notification-btn" aria-label="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="user-menu" data-action="toggle-user-menu" aria-label="قائمة المستخدم">
                        <div class="user-avatar">م</div>
                        <div class="user-info">
                            <div class="user-name">مدير النظام</div>
                            <div class="user-email"><EMAIL></div>
                        </div>
                        <i class="fas fa-chevron-down d-none d-md-inline"></i>
                    </div>
                </div>
            </header>

            <!-- منطقة المحتوى المحسنة -->
            <div class="content-area">
                <!-- وحدة لوحة التحكم -->
                <div id="dashboard-module" class="module-content active">
                    <!-- سيتم تحميل محتوى لوحة التحكم هنا -->
                </div>

                <!-- وحدة إدارة الأطباء -->
                <div id="doctors-module" class="module-content">
                    <!-- سيتم تحميل محتوى إدارة الأطباء هنا -->
                </div>

                <!-- وحدة إدارة الموظفين -->
                <div id="employees-module" class="module-content">
                    <!-- سيتم تحميل محتوى إدارة الموظفين هنا -->
                </div>

                <!-- وحدة نظام التركيبات -->
                <div id="prostheses-module" class="module-content">
                    <!-- سيتم تحميل محتوى نظام التركيبات هنا -->
                </div>

                <!-- وحدة النظام المالي -->
                <div id="financial-module" class="module-content">
                    <!-- سيتم تحميل محتوى النظام المالي هنا -->
                </div>

                <!-- وحدة التقارير والإحصائيات -->
                <div id="reports-module" class="module-content">
                    <!-- سيتم تحميل محتوى التقارير هنا -->
                </div>

                <!-- وحدة الإعدادات -->
                <div id="settings-module" class="module-content">
                    <div class="text-center p-5">
                        <i class="fas fa-cog text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h3 class="text-muted">وحدة الإعدادات</h3>
                        <p class="text-muted">ستكون متاحة في التحديث القادم</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- ملفات JavaScript المحسنة -->
    <script src="advanced-device-manager.js"></script>
    <script src="performance-optimizer.js"></script>
    <script src="advanced-mobile-interactions.js"></script>
    <script src="enhanced-auth-system.js"></script>
    <script src="enhanced-dashboard.js"></script>
    <script src="enhanced-doctors-management.js"></script>
    <script src="enhanced-employees-management.js"></script>
    <script src="enhanced-prostheses-system.js"></script>
    <script src="enhanced-financial-system.js"></script>
    <script src="enhanced-reports-system.js"></script>

    <!-- JavaScript الرئيسي المحسن -->
    <script>
        // فئة النظام الرئيسي المحسن
        class EnhancedDentalLabSystem {
            constructor() {
                this.currentModule = 'dashboard';
                this.isLoading = true;
                this.modules = new Map();
                this.loadingSteps = [
                    'تحميل النظام الأساسي...',
                    'تهيئة قاعدة البيانات...',
                    'تحميل الوحدات...',
                    'تطبيق التحسينات...',
                    'جاري الإنتهاء...'
                ];
                this.currentStep = 0;

                this.initialize();
            }

            /**
             * تهيئة النظام
             */
            async initialize() {
                try {
                    // انتظار تحميل DOM
                    if (document.readyState === 'loading') {
                        await new Promise(resolve => {
                            document.addEventListener('DOMContentLoaded', resolve);
                        });
                    }

                    // بدء عملية التحميل
                    await this.startLoadingProcess();

                    // تهيئة مدير الأجهزة
                    this.initializeDeviceManager();

                    // تهيئة محسن الأداء
                    this.initializePerformanceOptimizer();

                    // تهيئة التفاعلات المتقدمة
                    this.initializeMobileInteractions();

                    // إعداد مستمعي الأحداث
                    this.setupEventListeners();

                    // تحميل الوحدة الافتراضية
                    await this.loadModule('dashboard');

                    // إنهاء التحميل
                    await this.finishLoading();

                    console.log('Enhanced Dental Lab System initialized successfully');
                } catch (error) {
                    console.error('Failed to initialize system:', error);
                    this.showError('فشل في تحميل النظام. يرجى إعادة تحميل الصفحة.');
                }
            }

            /**
             * بدء عملية التحميل
             */
            async startLoadingProcess() {
                const statusElement = document.getElementById('loadingStatus');

                for (let i = 0; i < this.loadingSteps.length; i++) {
                    this.currentStep = i;
                    if (statusElement) {
                        statusElement.textContent = this.loadingSteps[i];
                    }

                    // محاكاة وقت التحميل
                    await new Promise(resolve => setTimeout(resolve, 600));
                }
            }

            /**
             * تهيئة مدير الأجهزة
             */
            initializeDeviceManager() {
                if (typeof deviceManager !== 'undefined') {
                    // الاستماع لأحداث الجهاز
                    document.addEventListener('screenSizeChange', (e) => {
                        this.handleScreenSizeChange(e.detail);
                    });

                    document.addEventListener('orientationChange', (e) => {
                        this.handleOrientationChange(e.detail);
                    });

                    document.addEventListener('connectionChange', (e) => {
                        this.handleConnectionChange(e.detail);
                    });
                }
            }

            /**
             * تهيئة محسن الأداء
             */
            initializePerformanceOptimizer() {
                if (typeof performanceOptimizer !== 'undefined') {
                    // تحسين الأداء عند التحميل
                    performanceOptimizer.optimize();

                    // الاستماع لأحداث الأداء
                    document.addEventListener('highMemoryUsage', () => {
                        this.handleHighMemoryUsage();
                    });

                    document.addEventListener('lowFrameRate', () => {
                        this.handleLowFrameRate();
                    });
                }
            }

            /**
             * تهيئة التفاعلات المتقدمة
             */
            initializeMobileInteractions() {
                if (typeof mobileInteractions !== 'undefined') {
                    // تسجيل معالجات الإيماءات
                    mobileInteractions.registerGestureHandler('swipe', (e) => {
                        this.handleSwipeGesture(e.detail);
                    });

                    mobileInteractions.registerGestureHandler('pinch', (e) => {
                        this.handlePinchGesture(e.detail);
                    });

                    mobileInteractions.registerGestureHandler('longPress', (e) => {
                        this.handleLongPressGesture(e.detail);
                    });
                }
            }

            /**
             * إعداد مستمعي الأحداث
             */
            setupEventListeners() {
                // تفويض الأحداث للأداء الأفضل
                document.body.addEventListener('click', this.handleClick.bind(this));
                document.body.addEventListener('keydown', this.handleKeydown.bind(this));

                // أحداث النافذة
                window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
                window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));

                // أحداث الرؤية
                document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
            }

            /**
             * معالجة النقرات
             */
            handleClick(event) {
                const target = event.target.closest('[data-action]');
                if (!target) return;

                const action = target.dataset.action;
                const module = target.dataset.module;

                switch (action) {
                    case 'toggle-sidebar':
                        this.toggleSidebar();
                        break;
                    case 'show-module':
                        if (module) {
                            this.showModule(module);
                        }
                        break;
                    case 'toggle-user-menu':
                        this.toggleUserMenu();
                        break;
                    case 'logout':
                        this.logout();
                        break;
                }
            }

            /**
             * معالجة اختصارات لوحة المفاتيح
             */
            handleKeydown(event) {
                // اختصارات لوحة المفاتيح للكمبيوتر
                if (deviceManager && deviceManager.device.type === 'desktop') {
                    if (event.ctrlKey || event.metaKey) {
                        switch (event.key) {
                            case '1':
                                event.preventDefault();
                                this.showModule('dashboard');
                                break;
                            case '2':
                                event.preventDefault();
                                this.showModule('doctors');
                                break;
                            case '3':
                                event.preventDefault();
                                this.showModule('employees');
                                break;
                            case '4':
                                event.preventDefault();
                                this.showModule('prostheses');
                                break;
                            case '5':
                                event.preventDefault();
                                this.showModule('financial');
                                break;
                            case '6':
                                event.preventDefault();
                                this.showModule('reports');
                                break;
                        }
                    }
                }

                // مفتاح Escape لإغلاق القوائم
                if (event.key === 'Escape') {
                    this.closeSidebar();
                    this.closeUserMenu();
                }
            }

            /**
             * تبديل الشريط الجانبي
             */
            toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');
                const mainContent = document.getElementById('mainContent');

                if (!sidebar) return;

                const isActive = sidebar.classList.contains('active');

                if (window.innerWidth <= 1024) {
                    // وضع الموبايل/التابلت
                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');

                    if (!isActive) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                } else {
                    // وضع سطح المكتب
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                }

                // تشغيل الاهتزاز التفاعلي
                this.triggerHapticFeedback('tap');
            }

            /**
             * إغلاق الشريط الجانبي
             */
            closeSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');

                if (sidebar) {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }

            /**
             * عرض وحدة معينة
             */
            async showModule(moduleName) {
                if (this.currentModule === moduleName) return;

                try {
                    // إخفاء جميع الوحدات
                    document.querySelectorAll('.module-content').forEach(module => {
                        module.classList.remove('active');
                    });

                    // إزالة التفعيل من جميع عناصر التنقل
                    document.querySelectorAll('.nav-item').forEach(item => {
                        item.classList.remove('active');
                    });

                    // تفعيل الوحدة المحددة
                    const moduleElement = document.getElementById(`${moduleName}-module`);
                    const navElement = document.querySelector(`[data-module="${moduleName}"]`);

                    if (moduleElement) {
                        moduleElement.classList.add('active');
                    }

                    if (navElement) {
                        navElement.classList.add('active');
                    }

                    // تحديث عنوان الصفحة
                    this.updatePageTitle(moduleName);

                    // تحميل محتوى الوحدة
                    await this.loadModule(moduleName);

                    // إغلاق الشريط الجانبي في الشاشات الصغيرة
                    if (window.innerWidth <= 1024) {
                        this.closeSidebar();
                    }

                    this.currentModule = moduleName;

                    // تشغيل الاهتزاز التفاعلي
                    this.triggerHapticFeedback('tap');

                } catch (error) {
                    console.error(`Failed to show module ${moduleName}:`, error);
                    this.showError(`فشل في تحميل وحدة ${this.getModuleName(moduleName)}`);
                }
            }

            /**
             * تحميل محتوى الوحدة
             */
            async loadModule(moduleName) {
                const moduleContainer = document.getElementById(`${moduleName}-module`);
                if (!moduleContainer) return;

                // التحقق من وجود المحتوى
                if (moduleContainer.children.length > 0 &&
                    !moduleContainer.querySelector('.text-center')) {
                    return; // المحتوى محمل بالفعل
                }

                try {
                    let content = '';

                    switch (moduleName) {
                        case 'dashboard':
                            if (typeof enhancedDashboard !== 'undefined') {
                                content = enhancedDashboard.generateDashboardHTML();
                                moduleContainer.innerHTML = content;
                                enhancedDashboard.initializeCharts();
                            } else {
                                content = this.generateDefaultDashboard();
                                moduleContainer.innerHTML = content;
                            }
                            break;

                        case 'doctors':
                            if (typeof doctorsManager !== 'undefined') {
                                content = doctorsManager.generateDoctorsManagementHTML();
                                moduleContainer.innerHTML = content;
                            } else {
                                content = this.generateDefaultDoctors();
                                moduleContainer.innerHTML = content;
                            }
                            break;

                        case 'employees':
                            if (typeof employeesManager !== 'undefined') {
                                content = employeesManager.generateEmployeesManagementHTML();
                                moduleContainer.innerHTML = content;
                            } else {
                                content = this.generateDefaultEmployees();
                                moduleContainer.innerHTML = content;
                            }
                            break;

                        case 'prostheses':
                            if (typeof prosthesesSystem !== 'undefined') {
                                content = prosthesesSystem.generateProsthesesSystemHTML();
                                moduleContainer.innerHTML = content;
                            } else {
                                content = this.generateDefaultProstheses();
                                moduleContainer.innerHTML = content;
                            }
                            break;

                        case 'financial':
                            if (typeof financialSystem !== 'undefined') {
                                content = financialSystem.generateFinancialSystemHTML();
                                moduleContainer.innerHTML = content;
                            } else {
                                content = this.generateDefaultFinancial();
                                moduleContainer.innerHTML = content;
                            }
                            break;

                        case 'reports':
                            if (typeof reportsSystem !== 'undefined') {
                                content = reportsSystem.generateReportsSystemHTML();
                                moduleContainer.innerHTML = content;
                            } else {
                                content = this.generateDefaultReports();
                                moduleContainer.innerHTML = content;
                            }
                            break;

                        default:
                            content = `
                                <div class="text-center p-5">
                                    <i class="fas fa-cog text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                                    <h3 class="text-muted">وحدة ${this.getModuleName(moduleName)}</h3>
                                    <p class="text-muted">ستكون متاحة في التحديث القادم</p>
                                </div>
                            `;
                            moduleContainer.innerHTML = content;
                    }

                    // تحسين الأداء بعد التحميل
                    if (typeof performanceOptimizer !== 'undefined') {
                        performanceOptimizer.queueAnimation(() => {
                            // تحسين العناصر الجديدة
                            this.optimizeNewElements(moduleContainer);
                        });
                    }

                } catch (error) {
                    console.error(`Failed to load module content for ${moduleName}:`, error);
                    moduleContainer.innerHTML = `
                        <div class="text-center p-5 text-danger">
                            <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h3>خطأ في التحميل</h3>
                            <p>فشل في تحميل محتوى الوحدة</p>
                            <button class="btn btn-primary" onclick="dentalLabSystem.loadModule('${moduleName}')">
                                إعادة المحاولة
                            </button>
                        </div>
                    `;
                }
            }

            /**
             * تحسين العناصر الجديدة
             */
            optimizeNewElements(container) {
                // إعداد التحميل الكسول للصور
                const images = container.querySelectorAll('img[data-src]');
                images.forEach(img => {
                    if (typeof performanceOptimizer !== 'undefined' &&
                        performanceOptimizer.observers.image) {
                        performanceOptimizer.observers.image.observe(img);
                    }
                });

                // إعداد التمرير الافتراضي للقوائم الطويلة
                const virtualScrollContainers = container.querySelectorAll('[data-virtual-scroll]');
                virtualScrollContainers.forEach(scrollContainer => {
                    if (typeof performanceOptimizer !== 'undefined') {
                        performanceOptimizer.enableVirtualScrolling(scrollContainer);
                    }
                });
            }

            /**
             * تحديث عنوان الصفحة
             */
            updatePageTitle(moduleName) {
                const moduleNames = {
                    dashboard: 'لوحة التحكم',
                    doctors: 'إدارة الأطباء',
                    employees: 'إدارة الموظفين',
                    prostheses: 'نظام التركيبات',
                    financial: 'النظام المالي',
                    reports: 'التقارير والإحصائيات',
                    settings: 'الإعدادات'
                };

                const moduleName_ar = moduleNames[moduleName] || moduleName;
                document.title = `${moduleName_ar} - نظام معمل الأسنان المتطور`;

                // تحديث مسار التنقل
                const breadcrumb = document.getElementById('currentModuleName');
                if (breadcrumb) {
                    breadcrumb.textContent = moduleName_ar;
                }
            }

            /**
             * الحصول على اسم الوحدة
             */
            getModuleName(moduleName) {
                const moduleNames = {
                    dashboard: 'لوحة التحكم',
                    doctors: 'إدارة الأطباء',
                    employees: 'إدارة الموظفين',
                    prostheses: 'نظام التركيبات',
                    financial: 'النظام المالي',
                    reports: 'التقارير والإحصائيات',
                    settings: 'الإعدادات'
                };
                return moduleNames[moduleName] || moduleName;
            }

            /**
             * إنهاء التحميل
             */
            async finishLoading() {
                await new Promise(resolve => setTimeout(resolve, 500));

                const loadingScreen = document.getElementById('loadingScreen');
                const mainContainer = document.getElementById('mainContainer');

                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                }

                if (mainContainer) {
                    mainContainer.style.display = 'block';
                }

                this.isLoading = false;

                // تشغيل الاهتزاز التفاعلي للترحيب
                this.triggerHapticFeedback('success');
            }

            /**
             * معالجة تغيير حجم الشاشة
             */
            handleScreenSizeChange(screenSize) {
                // تحديث التخطيط حسب حجم الشاشة
                if (screenSize.width <= 768) {
                    this.closeSidebar();
                }
            }

            /**
             * معالجة تغيير الاتجاه
             */
            handleOrientationChange(orientation) {
                // إعادة تخطيط المحتوى عند تغيير الاتجاه
                setTimeout(() => {
                    this.refreshCurrentModule();
                }, 100);
            }

            /**
             * معالجة تغيير الاتصال
             */
            handleConnectionChange(connection) {
                if (connection.saveData || connection.effectiveType === '2g') {
                    this.enableDataSavingMode();
                } else {
                    this.disableDataSavingMode();
                }
            }

            /**
             * معالجة ارتفاع استخدام الذاكرة
             */
            handleHighMemoryUsage() {
                // تنظيف الوحدات غير المستخدمة
                this.cleanupUnusedModules();

                // إظهار تنبيه للمستخدم
                this.showNotification('تم تحسين استخدام الذاكرة', 'info');
            }

            /**
             * معالجة انخفاض معدل الإطارات
             */
            handleLowFrameRate() {
                // تقليل الرسوم المتحركة
                document.body.classList.add('reduced-motion');

                // إظهار تنبيه للمستخدم
                this.showNotification('تم تحسين الأداء', 'info');
            }

            /**
             * معالجة إيماءة السحب
             */
            handleSwipeGesture(detail) {
                if (detail.direction === 'right' && window.innerWidth <= 768) {
                    this.toggleSidebar();
                } else if (detail.direction === 'left' && window.innerWidth <= 768) {
                    this.closeSidebar();
                }
            }

            /**
             * معالجة إيماءة القرص
             */
            handlePinchGesture(detail) {
                // منع التكبير الافتراضي في بعض المناطق
                if (detail.target && detail.target.closest('.chart-container')) {
                    // السماح بالتكبير في الرسوم البيانية
                    return;
                }

                // منع التكبير في باقي المناطق
                event.preventDefault();
            }

            /**
             * معالجة الضغط الطويل
             */
            handleLongPressGesture(detail) {
                // إظهار قائمة سياقية أو معلومات إضافية
                const target = detail.target;
                if (target && target.closest('.card')) {
                    this.showContextMenu(target, detail.x, detail.y);
                }
            }

            /**
             * تبديل قائمة المستخدم
             */
            toggleUserMenu() {
                // سيتم تنفيذها لاحقاً
                console.log('تبديل قائمة المستخدم');
            }

            /**
             * إغلاق قائمة المستخدم
             */
            closeUserMenu() {
                // سيتم تنفيذها لاحقاً
                console.log('إغلاق قائمة المستخدم');
            }

            /**
             * تسجيل الخروج
             */
            logout() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    // حفظ البيانات قبل الخروج
                    this.saveUserSession();

                    // إعادة تحميل الصفحة أو إعادة توجيه
                    location.reload();
                }
            }

            /**
             * معالجة تغيير حجم النافذة
             */
            handleResize() {
                // تحديث التخطيط
                this.updateLayout();

                // إعادة تهيئة الرسوم البيانية
                this.refreshCharts();
            }

            /**
             * معالجة ما قبل إغلاق النافذة
             */
            handleBeforeUnload(event) {
                // حفظ البيانات
                this.saveUserSession();

                // إظهار تحذير إذا كان هناك تغييرات غير محفوظة
                if (this.hasUnsavedChanges()) {
                    event.preventDefault();
                    event.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
                    return event.returnValue;
                }
            }

            /**
             * معالجة تغيير رؤية الصفحة
             */
            handleVisibilityChange() {
                if (document.hidden) {
                    // إيقاف العمليات غير الضرورية
                    this.pauseBackgroundTasks();
                } else {
                    // استئناف العمليات
                    this.resumeBackgroundTasks();
                }
            }

            /**
             * تشغيل الاهتزاز التفاعلي
             */
            triggerHapticFeedback(type) {
                if (typeof mobileInteractions !== 'undefined') {
                    mobileInteractions.triggerHapticFeedback(type);
                }
            }

            /**
             * إظهار إشعار
             */
            showNotification(message, type = 'info') {
                // إنشاء عنصر الإشعار
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                        <span>${message}</span>
                    </div>
                    <button class="notification-close" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                // إضافة الإشعار للصفحة
                document.body.appendChild(notification);

                // إزالة الإشعار تلقائياً
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            }

            /**
             * إظهار خطأ
             */
            showError(message) {
                this.showNotification(message, 'error');
            }

            /**
             * الحصول على أيقونة الإشعار
             */
            getNotificationIcon(type) {
                const icons = {
                    info: 'info-circle',
                    success: 'check-circle',
                    warning: 'exclamation-triangle',
                    error: 'times-circle'
                };
                return icons[type] || 'info-circle';
            }

            /**
             * تأخير التنفيذ
             */
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            /**
             * تنظيف الوحدات غير المستخدمة
             */
            cleanupUnusedModules() {
                // تنظيف محتوى الوحدات غير النشطة
                document.querySelectorAll('.module-content:not(.active)').forEach(module => {
                    if (module.children.length > 1) {
                        // الاحتفاظ بعنصر واحد فقط
                        while (module.children.length > 1) {
                            module.removeChild(module.lastChild);
                        }
                    }
                });
            }

            /**
             * تفعيل وضع توفير البيانات
             */
            enableDataSavingMode() {
                document.body.classList.add('data-saving-mode');
            }

            /**
             * إلغاء وضع توفير البيانات
             */
            disableDataSavingMode() {
                document.body.classList.remove('data-saving-mode');
            }

            /**
             * تحديث التخطيط
             */
            updateLayout() {
                // إعادة حساب أحجام العناصر
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');

                if (window.innerWidth > 1024) {
                    if (sidebar && !sidebar.classList.contains('collapsed')) {
                        mainContent.style.marginRight = '280px';
                    }
                } else {
                    mainContent.style.marginRight = '0';
                }
            }

            /**
             * تحديث الوحدة الحالية
             */
            refreshCurrentModule() {
                if (this.currentModule) {
                    this.loadModule(this.currentModule);
                }
            }

            /**
             * تحديث الرسوم البيانية
             */
            refreshCharts() {
                // إعادة تهيئة الرسوم البيانية في الوحدة النشطة
                const activeModule = document.querySelector('.module-content.active');
                if (activeModule) {
                    const charts = activeModule.querySelectorAll('canvas');
                    charts.forEach(chart => {
                        if (chart.chart) {
                            chart.chart.resize();
                        }
                    });
                }
            }

            /**
             * حفظ جلسة المستخدم
             */
            saveUserSession() {
                const sessionData = {
                    currentModule: this.currentModule,
                    timestamp: Date.now()
                };
                localStorage.setItem('user_session', JSON.stringify(sessionData));
            }

            /**
             * التحقق من وجود تغييرات غير محفوظة
             */
            hasUnsavedChanges() {
                // التحقق من وجود نماذج معدلة
                const forms = document.querySelectorAll('form');
                for (const form of forms) {
                    if (form.classList.contains('modified')) {
                        return true;
                    }
                }
                return false;
            }

            /**
             * إيقاف المهام الخلفية
             */
            pauseBackgroundTasks() {
                // إيقاف التحديثات التلقائية
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }
            }

            /**
             * استئناف المهام الخلفية
             */
            resumeBackgroundTasks() {
                // استئناف التحديثات التلقائية
                this.updateInterval = setInterval(() => {
                    this.updateData();
                }, 30000); // كل 30 ثانية
            }

            /**
             * تحديث البيانات
             */
            updateData() {
                // تحديث البيانات في الخلفية
                if (this.currentModule === 'dashboard' && typeof enhancedDashboard !== 'undefined') {
                    enhancedDashboard.updateData();
                }
            }

            /**
             * إنشاء لوحة تحكم افتراضية
             */
            generateDefaultDashboard() {
                return `
                    <div class="dashboard-container">
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="welcome-card">
                                    <div class="welcome-content">
                                        <h2>مرحباً بك في نظام معمل الأسنان المتطور</h2>
                                        <p>الإصدار الثاني المحسن للعمل على جميع الأجهزة</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 col-md-6 col-lg-3 mb-3">
                                <div class="stats-card stats-primary">
                                    <div class="stats-icon"><i class="fas fa-tooth"></i></div>
                                    <div class="stats-content">
                                        <div class="stats-number">0</div>
                                        <div class="stats-label">التركيبات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-3 mb-3">
                                <div class="stats-card stats-success">
                                    <div class="stats-icon"><i class="fas fa-user-md"></i></div>
                                    <div class="stats-content">
                                        <div class="stats-number">0</div>
                                        <div class="stats-label">الأطباء</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-3 mb-3">
                                <div class="stats-card stats-info">
                                    <div class="stats-icon"><i class="fas fa-users"></i></div>
                                    <div class="stats-content">
                                        <div class="stats-number">0</div>
                                        <div class="stats-label">الموظفون</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-3 mb-3">
                                <div class="stats-card stats-warning">
                                    <div class="stats-icon"><i class="fas fa-chart-line"></i></div>
                                    <div class="stats-content">
                                        <div class="stats-number">0</div>
                                        <div class="stats-label">الإيرادات</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            /**
             * إنشاء صفحة أطباء افتراضية
             */
            generateDefaultDoctors() {
                return `
                    <div class="doctors-container">
                        <div class="page-header mb-4">
                            <h2><i class="fas fa-user-md text-primary me-2"></i>إدارة الأطباء</h2>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة طبيب جديد
                            </button>
                        </div>
                        <div class="empty-state text-center p-5">
                            <i class="fas fa-user-md text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4 class="text-muted">لا يوجد أطباء</h4>
                            <p class="text-muted">ابدأ بإضافة أول طبيب للنظام</p>
                        </div>
                    </div>
                `;
            }

            /**
             * إنشاء صفحة موظفين افتراضية
             */
            generateDefaultEmployees() {
                return `
                    <div class="employees-container">
                        <div class="page-header mb-4">
                            <h2><i class="fas fa-users text-primary me-2"></i>إدارة الموظفين</h2>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة موظف جديد
                            </button>
                        </div>
                        <div class="empty-state text-center p-5">
                            <i class="fas fa-users text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4 class="text-muted">لا يوجد موظفون</h4>
                            <p class="text-muted">ابدأ بإضافة أول موظف للنظام</p>
                        </div>
                    </div>
                `;
            }

            /**
             * إنشاء صفحة تركيبات افتراضية
             */
            generateDefaultProstheses() {
                return `
                    <div class="prostheses-container">
                        <div class="page-header mb-4">
                            <h2><i class="fas fa-tooth text-primary me-2"></i>نظام التركيبات</h2>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة تركيب جديد
                            </button>
                        </div>
                        <div class="empty-state text-center p-5">
                            <i class="fas fa-tooth text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4 class="text-muted">لا يوجد تركيبات</h4>
                            <p class="text-muted">ابدأ بإضافة أول تركيب للنظام</p>
                        </div>
                    </div>
                `;
            }

            /**
             * إنشاء صفحة مالية افتراضية
             */
            generateDefaultFinancial() {
                return `
                    <div class="financial-container">
                        <div class="page-header mb-4">
                            <h2><i class="fas fa-chart-line text-primary me-2"></i>النظام المالي</h2>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة معاملة جديدة
                            </button>
                        </div>
                        <div class="empty-state text-center p-5">
                            <i class="fas fa-chart-line text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4 class="text-muted">لا يوجد معاملات مالية</h4>
                            <p class="text-muted">ابدأ بإضافة أول معاملة للنظام</p>
                        </div>
                    </div>
                `;
            }

            /**
             * إنشاء صفحة تقارير افتراضية
             */
            generateDefaultReports() {
                return `
                    <div class="reports-container">
                        <div class="page-header mb-4">
                            <h2><i class="fas fa-chart-bar text-primary me-2"></i>التقارير والإحصائيات</h2>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إنشاء تقرير جديد
                            </button>
                        </div>
                        <div class="empty-state text-center p-5">
                            <i class="fas fa-chart-bar text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4 class="text-muted">لا يوجد تقارير</h4>
                            <p class="text-muted">ابدأ بإنشاء أول تقرير للنظام</p>
                        </div>
                    </div>
                `;
            }

            /**
             * إظهار قائمة سياقية
             */
            showContextMenu(target, x, y) {
                // إنشاء قائمة سياقية
                const contextMenu = document.createElement('div');
                contextMenu.className = 'context-menu';
                contextMenu.style.cssText = `
                    position: fixed;
                    top: ${y}px;
                    left: ${x}px;
                    background: white;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                    z-index: 10000;
                    padding: 0.5rem 0;
                    min-width: 150px;
                `;

                contextMenu.innerHTML = `
                    <div class="context-menu-item" onclick="console.log('عرض التفاصيل')">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </div>
                    <div class="context-menu-item" onclick="console.log('تعديل')">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </div>
                    <div class="context-menu-item" onclick="console.log('حذف')">
                        <i class="fas fa-trash"></i>
                        حذف
                    </div>
                `;

                document.body.appendChild(contextMenu);

                // إزالة القائمة عند النقر خارجها
                setTimeout(() => {
                    document.addEventListener('click', function removeContextMenu() {
                        if (contextMenu.parentElement) {
                            contextMenu.remove();
                        }
                        document.removeEventListener('click', removeContextMenu);
                    });
                }, 100);
            }
        }

        // إنشاء مثيل عام من النظام
        const dentalLabSystem = new EnhancedDentalLabSystem();

        // إضافة أنماط CSS للإشعارات والقوائم السياقية
        const additionalStyles = document.createElement('style');
        additionalStyles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                padding: 1rem;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 1rem;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            }

            .notification-info { border-left: 4px solid #3b82f6; }
            .notification-success { border-left: 4px solid #10b981; }
            .notification-warning { border-left: 4px solid #f59e0b; }
            .notification-error { border-left: 4px solid #ef4444; }

            .notification-content {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                flex: 1;
            }

            .notification-close {
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 4px;
            }

            .notification-close:hover {
                background: #f3f4f6;
            }

            .context-menu-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1rem;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .context-menu-item:hover {
                background: #f3f4f6;
            }

            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @media (max-width: 768px) {
                .notification {
                    right: 10px;
                    left: 10px;
                    min-width: auto;
                }
            }
        `;
        document.head.appendChild(additionalStyles);
    </script>
</body>
</html>
