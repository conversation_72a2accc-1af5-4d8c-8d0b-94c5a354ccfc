/**
 * نظام التركيبات المتقدم
 * Enhanced Prostheses System
 * 
 * المميزات:
 * - مخطط الأسنان التفاعلي المطور
 * - تتبع مراحل الإنتاج
 * - نظام التصوير والمرفقات
 * - إدارة الجودة والفحص
 * - تقارير مفصلة للتركيبات
 * - نظام التنبيهات الذكي
 */

class EnhancedProsthesesSystem {
    constructor() {
        this.prostheses = [];
        this.prosthesisTypes = [];
        this.productionStages = [];
        this.qualityChecks = [];
        this.attachments = [];
        this.loadData();
        this.initializeProsthesisTypes();
        this.initializeProductionStages();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        this.prostheses = data.prostheses || [];
        this.prosthesisTypes = data.prosthesisTypes || [];
        this.productionStages = data.productionStages || [];
        this.qualityChecks = data.qualityChecks || [];
        this.attachments = data.attachments || [];
    }

    /**
     * حفظ البيانات
     */
    saveData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        data.prostheses = this.prostheses;
        data.prosthesisTypes = this.prosthesisTypes;
        data.productionStages = this.productionStages;
        data.qualityChecks = this.qualityChecks;
        data.attachments = this.attachments;
        localStorage.setItem('dental_lab_data', JSON.stringify(data));
    }

    /**
     * تهيئة أنواع التركيبات
     */
    initializeProsthesisTypes() {
        if (this.prosthesisTypes.length === 0) {
            this.prosthesisTypes = [
                {
                    id: 1,
                    name: 'تاج معدني',
                    category: 'crown',
                    material: 'metal',
                    basePrice: 500,
                    productionTime: 3,
                    description: 'تاج معدني عالي الجودة'
                },
                {
                    id: 2,
                    name: 'تاج خزفي',
                    category: 'crown',
                    material: 'ceramic',
                    basePrice: 800,
                    productionTime: 5,
                    description: 'تاج خزفي بمظهر طبيعي'
                },
                {
                    id: 3,
                    name: 'جسر ثلاثي',
                    category: 'bridge',
                    material: 'ceramic',
                    basePrice: 2000,
                    productionTime: 7,
                    description: 'جسر خزفي لثلاث وحدات'
                },
                {
                    id: 4,
                    name: 'طقم جزئي',
                    category: 'partial_denture',
                    material: 'acrylic',
                    basePrice: 1200,
                    productionTime: 10,
                    description: 'طقم جزئي متحرك'
                },
                {
                    id: 5,
                    name: 'طقم كامل',
                    category: 'complete_denture',
                    material: 'acrylic',
                    basePrice: 2500,
                    productionTime: 14,
                    description: 'طقم كامل للفك'
                },
                {
                    id: 6,
                    name: 'عدسة خزفية',
                    category: 'veneer',
                    material: 'ceramic',
                    basePrice: 1000,
                    productionTime: 4,
                    description: 'عدسة خزفية رقيقة'
                }
            ];
            this.saveData();
        }
    }

    /**
     * تهيئة مراحل الإنتاج
     */
    initializeProductionStages() {
        if (this.productionStages.length === 0) {
            this.productionStages = [
                {
                    id: 1,
                    name: 'استلام الطبعة',
                    order: 1,
                    estimatedDuration: 0.5,
                    description: 'فحص واستلام الطبعة من الطبيب'
                },
                {
                    id: 2,
                    name: 'صب النموذج',
                    order: 2,
                    estimatedDuration: 1,
                    description: 'صب النموذج الجبسي'
                },
                {
                    id: 3,
                    name: 'التصميم والتخطيط',
                    order: 3,
                    estimatedDuration: 2,
                    description: 'تصميم التركيبة وتخطيط العمل'
                },
                {
                    id: 4,
                    name: 'التشكيل الأولي',
                    order: 4,
                    estimatedDuration: 4,
                    description: 'تشكيل الهيكل الأساسي'
                },
                {
                    id: 5,
                    name: 'البناء والتشكيل',
                    order: 5,
                    estimatedDuration: 6,
                    description: 'بناء التركيبة وتشكيلها'
                },
                {
                    id: 6,
                    name: 'التلميع والتشطيب',
                    order: 6,
                    estimatedDuration: 2,
                    description: 'تلميع وتشطيب التركيبة'
                },
                {
                    id: 7,
                    name: 'فحص الجودة',
                    order: 7,
                    estimatedDuration: 1,
                    description: 'فحص نهائي للجودة'
                },
                {
                    id: 8,
                    name: 'التعبئة والتسليم',
                    order: 8,
                    estimatedDuration: 0.5,
                    description: 'تعبئة التركيبة وإعدادها للتسليم'
                }
            ];
            this.saveData();
        }
    }

    /**
     * إنشاء HTML لنظام التركيبات المتقدم
     */
    generateProsthesesSystemHTML() {
        return `
            <div class="enhanced-prostheses-system">
                <!-- رأس النظام -->
                <div class="system-header">
                    <div class="header-content">
                        <h1 class="system-title">
                            <i class="fas fa-tooth"></i>
                            نظام التركيبات المتقدم
                        </h1>
                        <p class="system-subtitle">
                            إدارة شاملة للتركيبات مع تتبع المراحل والجودة
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="prosthesesSystem.showAddProsthesisModal()">
                            <i class="fas fa-plus"></i>
                            تركيبة جديدة
                        </button>
                        <button class="btn btn-info" onclick="prosthesesSystem.showProductionBoard()">
                            <i class="fas fa-tasks"></i>
                            لوحة الإنتاج
                        </button>
                        <button class="btn btn-success" onclick="prosthesesSystem.showQualityControl()">
                            <i class="fas fa-check-circle"></i>
                            مراقبة الجودة
                        </button>
                        <button class="btn btn-secondary" onclick="prosthesesSystem.exportProsthesesData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="prostheses-stats">
                    ${this.generateProsthesesStats()}
                </div>

                <!-- تبويبات النظام -->
                <div class="system-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="prosthesesSystem.switchSystemTab('list')">
                            <i class="fas fa-list"></i>
                            قائمة التركيبات
                        </button>
                        <button class="tab-btn" onclick="prosthesesSystem.switchSystemTab('production')">
                            <i class="fas fa-cogs"></i>
                            مراحل الإنتاج
                        </button>
                        <button class="tab-btn" onclick="prosthesesSystem.switchSystemTab('quality')">
                            <i class="fas fa-award"></i>
                            الجودة والفحص
                        </button>
                        <button class="tab-btn" onclick="prosthesesSystem.switchSystemTab('analytics')">
                            <i class="fas fa-chart-bar"></i>
                            التحليلات
                        </button>
                        <button class="tab-btn" onclick="prosthesesSystem.switchSystemTab('settings')">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </button>
                    </div>

                    <!-- تبويب قائمة التركيبات -->
                    <div id="list-system-tab" class="tab-content active">
                        ${this.generateProsthesesListTab()}
                    </div>

                    <!-- تبويب مراحل الإنتاج -->
                    <div id="production-system-tab" class="tab-content">
                        ${this.generateProductionTab()}
                    </div>

                    <!-- تبويب الجودة والفحص -->
                    <div id="quality-system-tab" class="tab-content">
                        ${this.generateQualityTab()}
                    </div>

                    <!-- تبويب التحليلات -->
                    <div id="analytics-system-tab" class="tab-content">
                        ${this.generateAnalyticsTab()}
                    </div>

                    <!-- تبويب الإعدادات -->
                    <div id="settings-system-tab" class="tab-content">
                        ${this.generateSettingsTab()}
                    </div>
                </div>

                <!-- النوافذ المنبثقة -->
                ${this.generateModals()}
            </div>
        `;
    }

    /**
     * إنشاء إحصائيات التركيبات
     */
    generateProsthesesStats() {
        const totalProstheses = this.prostheses.length;
        const inProgress = this.prostheses.filter(p => p.status === 'in_progress').length;
        const completed = this.prostheses.filter(p => p.status === 'completed').length;
        const overdue = this.getOverdueProstheses().length;
        const avgCompletionTime = this.calculateAverageCompletionTime();
        const qualityScore = this.calculateAverageQualityScore();

        return `
            <div class="stats-cards">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-tooth"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${totalProstheses}</div>
                        <div class="stat-label">إجمالي التركيبات</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-calendar"></i>
                            هذا الشهر
                        </div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${inProgress}</div>
                        <div class="stat-label">قيد الإنتاج</div>
                        <div class="stat-change ${inProgress > totalProstheses * 0.6 ? 'negative' : 'neutral'}">
                            <i class="fas fa-percentage"></i>
                            ${totalProstheses > 0 ? Math.round((inProgress / totalProstheses) * 100) : 0}% من الإجمالي
                        </div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${completed}</div>
                        <div class="stat-label">مكتملة</div>
                        <div class="stat-change positive">
                            <i class="fas fa-thumbs-up"></i>
                            ${totalProstheses > 0 ? Math.round((completed / totalProstheses) * 100) : 0}% معدل الإنجاز
                        </div>
                    </div>
                </div>

                <div class="stat-card danger">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${overdue}</div>
                        <div class="stat-label">متأخرة</div>
                        <div class="stat-change ${overdue > 0 ? 'negative' : 'positive'}">
                            <i class="fas fa-${overdue > 0 ? 'exclamation-circle' : 'check-circle'}"></i>
                            ${overdue > 0 ? 'يحتاج إجراء فوري' : 'لا توجد متأخرات'}
                        </div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${avgCompletionTime.toFixed(1)}</div>
                        <div class="stat-label">متوسط وقت الإنجاز (يوم)</div>
                        <div class="stat-change ${avgCompletionTime <= 7 ? 'positive' : avgCompletionTime <= 14 ? 'neutral' : 'negative'}">
                            <i class="fas fa-${avgCompletionTime <= 7 ? 'rocket' : avgCompletionTime <= 14 ? 'clock' : 'turtle'}"></i>
                            ${avgCompletionTime <= 7 ? 'سريع' : avgCompletionTime <= 14 ? 'متوسط' : 'بطيء'}
                        </div>
                    </div>
                </div>

                <div class="stat-card secondary">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${qualityScore.toFixed(1)}%</div>
                        <div class="stat-label">درجة الجودة</div>
                        <div class="stat-change ${qualityScore >= 90 ? 'positive' : qualityScore >= 75 ? 'neutral' : 'negative'}">
                            <i class="fas fa-${qualityScore >= 90 ? 'medal' : qualityScore >= 75 ? 'award' : 'exclamation-triangle'}"></i>
                            ${qualityScore >= 90 ? 'ممتاز' : qualityScore >= 75 ? 'جيد' : 'يحتاج تحسين'}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء تبويب قائمة التركيبات
     */
    generateProsthesesListTab() {
        return `
            <div class="prostheses-list-content">
                <!-- شريط البحث والفلاتر -->
                <div class="search-filters-section">
                    <div class="search-bar">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" id="prostheses-search" placeholder="البحث في التركيبات..." 
                                   onkeyup="prosthesesSystem.filterProstheses()">
                        </div>
                    </div>
                    <div class="filters-group">
                        <select id="status-filter" onchange="prosthesesSystem.filterProstheses()">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="in_progress">قيد الإنتاج</option>
                            <option value="quality_check">فحص الجودة</option>
                            <option value="completed">مكتملة</option>
                            <option value="delivered">مسلمة</option>
                        </select>
                        <select id="type-filter" onchange="prosthesesSystem.filterProstheses()">
                            <option value="">جميع الأنواع</option>
                            ${this.prosthesisTypes.map(type => 
                                `<option value="${type.id}">${type.name}</option>`
                            ).join('')}
                        </select>
                        <select id="priority-filter" onchange="prosthesesSystem.filterProstheses()">
                            <option value="">جميع الأولويات</option>
                            <option value="urgent">عاجل</option>
                            <option value="high">عالي</option>
                            <option value="normal">عادي</option>
                            <option value="low">منخفض</option>
                        </select>
                        <input type="date" id="date-filter" onchange="prosthesesSystem.filterProstheses()" 
                               title="تاريخ التسليم">
                    </div>
                    <div class="view-options">
                        <button class="btn btn-sm btn-outline ${this.currentView === 'grid' ? 'active' : ''}" 
                                onclick="prosthesesSystem.switchView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn btn-sm btn-outline ${this.currentView === 'table' ? 'active' : ''}" 
                                onclick="prosthesesSystem.switchView('table')">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- عرض التركيبات -->
                <div class="prostheses-view" id="prostheses-view">
                    ${this.generateProsthesesView()}
                </div>
            </div>
        `;
    }

    /**
     * الحصول على التركيبات المتأخرة
     */
    getOverdueProstheses() {
        const now = new Date();
        return this.prostheses.filter(p => {
            if (p.status === 'completed' || p.status === 'delivered') return false;
            const deliveryDate = new Date(p.deliveryDate);
            return deliveryDate < now;
        });
    }

    /**
     * حساب متوسط وقت الإنجاز
     */
    calculateAverageCompletionTime() {
        const completedProstheses = this.prostheses.filter(p =>
            p.status === 'completed' || p.status === 'delivered'
        );

        if (completedProstheses.length === 0) return 0;

        const totalDays = completedProstheses.reduce((sum, p) => {
            const startDate = new Date(p.created_at);
            const endDate = new Date(p.completedAt || p.deliveredAt);
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return sum + diffDays;
        }, 0);

        return totalDays / completedProstheses.length;
    }

    /**
     * حساب متوسط درجة الجودة
     */
    calculateAverageQualityScore() {
        const qualityChecks = this.qualityChecks.filter(q => q.score !== undefined);

        if (qualityChecks.length === 0) return 85; // درجة افتراضية

        const totalScore = qualityChecks.reduce((sum, q) => sum + q.score, 0);
        return totalScore / qualityChecks.length;
    }

    /**
     * إنشاء عرض التركيبات
     */
    generateProsthesesView() {
        if (this.prostheses.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-tooth"></i>
                    <h3>لا توجد تركيبات مسجلة</h3>
                    <p>ابدأ بإضافة أول تركيبة للنظام</p>
                    <button class="btn btn-primary" onclick="prosthesesSystem.showAddProsthesisModal()">
                        <i class="fas fa-plus"></i>
                        إضافة تركيبة جديدة
                    </button>
                </div>
            `;
        }

        return this.currentView === 'grid' ?
            this.generateProsthesesGrid() :
            this.generateProsthesesTable();
    }

    /**
     * إنشاء شبكة التركيبات
     */
    generateProsthesesGrid() {
        return `
            <div class="prostheses-grid">
                ${this.prostheses.map(prosthesis => this.generateProsthesisCard(prosthesis)).join('')}
            </div>
        `;
    }

    /**
     * إنشاء بطاقة تركيبة
     */
    generateProsthesisCard(prosthesis) {
        const type = this.prosthesisTypes.find(t => t.id === prosthesis.typeId);
        const progress = this.calculateProsthesisProgress(prosthesis.id);
        const statusClass = this.getStatusClass(prosthesis.status);
        const priorityClass = this.getPriorityClass(prosthesis.priority);
        const isOverdue = this.isProsthesisOverdue(prosthesis);

        return `
            <div class="prosthesis-card ${statusClass} ${priorityClass} ${isOverdue ? 'overdue' : ''}"
                 data-prosthesis-id="${prosthesis.id}">

                <!-- رأس البطاقة -->
                <div class="prosthesis-header">
                    <div class="prosthesis-info">
                        <h3 class="prosthesis-title">
                            ${prosthesis.caseId || `تركيبة #${prosthesis.id}`}
                        </h3>
                        <p class="prosthesis-type">${type ? type.name : 'غير محدد'}</p>
                        <p class="prosthesis-patient">${prosthesis.patientName}</p>
                    </div>
                    <div class="prosthesis-status">
                        <span class="status-badge ${statusClass}">
                            ${this.getStatusText(prosthesis.status)}
                        </span>
                        ${prosthesis.priority !== 'normal' ?
                            `<span class="priority-badge ${priorityClass}">
                                ${this.getPriorityText(prosthesis.priority)}
                            </span>` : ''
                        }
                    </div>
                </div>

                <!-- مخطط الأسنان المصغر -->
                <div class="mini-tooth-diagram">
                    ${this.generateMiniToothDiagram(prosthesis.selectedTeeth)}
                </div>

                <!-- شريط التقدم -->
                <div class="progress-section">
                    <div class="progress-info">
                        <span class="progress-label">التقدم</span>
                        <span class="progress-percentage">${progress.percentage}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress.percentage}%"></div>
                    </div>
                    <div class="current-stage">
                        المرحلة الحالية: ${progress.currentStage}
                    </div>
                </div>

                <!-- معلومات التوقيت -->
                <div class="timing-info">
                    <div class="timing-item">
                        <i class="fas fa-calendar-plus"></i>
                        <span>تاريخ البدء: ${this.formatDate(prosthesis.created_at)}</span>
                    </div>
                    <div class="timing-item">
                        <i class="fas fa-calendar-check"></i>
                        <span>موعد التسليم: ${this.formatDate(prosthesis.deliveryDate)}</span>
                    </div>
                    ${isOverdue ?
                        `<div class="timing-item overdue">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>متأخر ${this.getDaysOverdue(prosthesis)} يوم</span>
                        </div>` : ''
                    }
                </div>

                <!-- معلومات إضافية -->
                <div class="additional-info">
                    <div class="info-item">
                        <i class="fas fa-user-md"></i>
                        <span>${prosthesis.doctorName || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>${prosthesis.totalPrice ? prosthesis.totalPrice.toLocaleString() + ' ج.م' : 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-teeth"></i>
                        <span>${prosthesis.selectedTeeth ? prosthesis.selectedTeeth.length : 0} سن</span>
                    </div>
                </div>

                <!-- إجراءات البطاقة -->
                <div class="prosthesis-actions">
                    <button class="btn btn-sm btn-primary" onclick="prosthesesSystem.viewProsthesisDetails(${prosthesis.id})">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="prosthesesSystem.editProsthesis(${prosthesis.id})">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-info" onclick="prosthesesSystem.updateProgress(${prosthesis.id})">
                        <i class="fas fa-tasks"></i>
                        التقدم
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء مخطط الأسنان المصغر
     */
    generateMiniToothDiagram(selectedTeeth) {
        if (!selectedTeeth || selectedTeeth.length === 0) {
            return '<div class="mini-diagram-empty">لم يتم تحديد أسنان</div>';
        }

        // إنشاء مخطط مبسط للأسنان المختارة
        const teethSections = {
            'upper-right': [],
            'upper-left': [],
            'lower-right': [],
            'lower-left': []
        };

        selectedTeeth.forEach(toothNumber => {
            const section = this.getToothSection(toothNumber);
            if (section) {
                teethSections[section].push(toothNumber);
            }
        });

        return `
            <div class="mini-diagram">
                <div class="mini-jaw upper">
                    <div class="mini-section">${teethSections['upper-right'].length}</div>
                    <div class="mini-separator">|</div>
                    <div class="mini-section">${teethSections['upper-left'].length}</div>
                </div>
                <div class="mini-jaw lower">
                    <div class="mini-section">${teethSections['lower-right'].length}</div>
                    <div class="mini-separator">|</div>
                    <div class="mini-section">${teethSections['lower-left'].length}</div>
                </div>
            </div>
        `;
    }

    /**
     * حساب تقدم التركيبة
     */
    calculateProsthesisProgress(prosthesisId) {
        const prosthesis = this.prostheses.find(p => p.id === prosthesisId);
        if (!prosthesis) return { percentage: 0, currentStage: 'غير محدد' };

        const completedStages = prosthesis.completedStages || [];
        const totalStages = this.productionStages.length;
        const percentage = Math.round((completedStages.length / totalStages) * 100);

        const currentStageId = completedStages.length < totalStages ?
            this.productionStages[completedStages.length].id :
            this.productionStages[totalStages - 1].id;

        const currentStage = this.productionStages.find(s => s.id === currentStageId);

        return {
            percentage: percentage,
            currentStage: currentStage ? currentStage.name : 'مكتمل'
        };
    }

    /**
     * الحصول على فئة الحالة
     */
    getStatusClass(status) {
        const statusClasses = {
            'pending': 'status-pending',
            'in_progress': 'status-progress',
            'quality_check': 'status-quality',
            'completed': 'status-completed',
            'delivered': 'status-delivered',
            'cancelled': 'status-cancelled'
        };
        return statusClasses[status] || 'status-unknown';
    }

    /**
     * الحصول على فئة الأولوية
     */
    getPriorityClass(priority) {
        const priorityClasses = {
            'urgent': 'priority-urgent',
            'high': 'priority-high',
            'normal': 'priority-normal',
            'low': 'priority-low'
        };
        return priorityClasses[priority] || 'priority-normal';
    }

    /**
     * الحصول على نص الحالة
     */
    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'in_progress': 'قيد الإنتاج',
            'quality_check': 'فحص الجودة',
            'completed': 'مكتملة',
            'delivered': 'مسلمة',
            'cancelled': 'ملغية'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * الحصول على نص الأولوية
     */
    getPriorityText(priority) {
        const priorityTexts = {
            'urgent': 'عاجل',
            'high': 'عالي',
            'normal': 'عادي',
            'low': 'منخفض'
        };
        return priorityTexts[priority] || 'عادي';
    }

    /**
     * التحقق من تأخر التركيبة
     */
    isProsthesisOverdue(prosthesis) {
        if (prosthesis.status === 'completed' || prosthesis.status === 'delivered') {
            return false;
        }
        const now = new Date();
        const deliveryDate = new Date(prosthesis.deliveryDate);
        return deliveryDate < now;
    }

    /**
     * الحصول على عدد أيام التأخير
     */
    getDaysOverdue(prosthesis) {
        const now = new Date();
        const deliveryDate = new Date(prosthesis.deliveryDate);
        const diffTime = Math.abs(now - deliveryDate);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * الحصول على قسم السن
     */
    getToothSection(toothNumber) {
        const quadrant = Math.floor(toothNumber / 10);
        switch (quadrant) {
            case 1: return 'upper-right';
            case 2: return 'upper-left';
            case 3: return 'lower-left';
            case 4: return 'lower-right';
            default: return null;
        }
    }

    /**
     * تبديل التبويبات
     */
    switchSystemTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.system-tabs .tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.system-tabs .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-system-tab`).classList.add('active');
        event.target.classList.add('active');
    }

    /**
     * تبديل العرض
     */
    switchView(viewType) {
        this.currentView = viewType;
        document.getElementById('prostheses-view').innerHTML = this.generateProsthesesView();

        // تحديث أزرار العرض
        document.querySelectorAll('.view-options .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    /**
     * فلترة التركيبات
     */
    filterProstheses() {
        // سيتم تنفيذها لاحقاً
        console.log('فلترة التركيبات');
    }

    /**
     * عرض نافذة إضافة تركيبة
     */
    showAddProsthesisModal() {
        alert('ميزة إضافة تركيبة ستكون متاحة قريباً');
    }

    /**
     * تصدير بيانات التركيبات
     */
    exportProsthesesData() {
        const exportData = {
            prostheses: this.prostheses,
            prosthesisTypes: this.prosthesisTypes,
            productionStages: this.productionStages,
            qualityChecks: this.qualityChecks,
            attachments: this.attachments,
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `prostheses-data-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }
}

// إنشاء مثيل عام من نظام التركيبات
const prosthesesSystem = new EnhancedProsthesesSystem();
prosthesesSystem.currentView = 'grid'; // العرض الافتراضي

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedProsthesesSystem, prosthesesSystem };
}
