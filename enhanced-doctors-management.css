/**
 * أنماط وحدة إدارة الأطباء المحسنة
 * Enhanced Doctors Management Styles
 */

/* الحاوي الرئيسي */
.enhanced-doctors-management {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    direction: rtl;
}

/* رأس الوحدة */
.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.module-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.module-title i {
    color: #667eea;
}

.module-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* إحصائيات الأطباء */
.doctors-stats {
    margin-bottom: 2rem;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* البحث والفلاتر */
.search-filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-bar {
    flex: 1;
    min-width: 300px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-group i {
    position: absolute;
    right: 1rem;
    color: #a0aec0;
    font-size: 1.1rem;
}

.search-input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.search-input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filters-group select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filters-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* شبكة الأطباء */
.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* بطاقة الطبيب */
.doctor-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.doctor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--status-color);
}

.doctor-card.active { --status-color: #48bb78; }
.doctor-card.inactive { --status-color: #a0aec0; }
.doctor-card.suspended { --status-color: #f56565; }

.doctor-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* رأس بطاقة الطبيب */
.doctor-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.doctor-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
    overflow: hidden;
}

.doctor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.active { background: #48bb78; }
.status-indicator.inactive { background: #a0aec0; }
.status-indicator.suspended { background: #f56565; }

.doctor-basic-info {
    flex: 1;
}

.doctor-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.25rem 0;
}

.doctor-specialty {
    color: #718096;
    font-size: 0.9rem;
    margin: 0 0 0.5rem 0;
}

.doctor-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.star-rating {
    display: flex;
    gap: 2px;
}

.star-rating i {
    color: #ffd700;
    font-size: 0.9rem;
}

.rating-text {
    color: #718096;
    font-size: 0.85rem;
}

.doctor-status {
    align-self: flex-start;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #c6f6d5;
    color: #38a169;
}

.status-badge.inactive {
    background: #e2e8f0;
    color: #718096;
}

.status-badge.suspended {
    background: #fed7d7;
    color: #e53e3e;
}

/* إحصائيات الطبيب */
.doctor-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-item i {
    display: block;
    font-size: 1.2rem;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: #718096;
}

/* معلومات الاتصال */
.doctor-contact {
    margin-bottom: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #4a5568;
}

.contact-item i {
    color: #a0aec0;
    width: 16px;
    text-align: center;
}

/* إجراءات الطبيب */
.doctor-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    position: relative;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-width: 180px;
    z-index: 1000;
    display: none;
    padding: 0.5rem 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.dropdown-menu a:hover {
    background: #f7fafc;
}

.dropdown-menu a.text-danger {
    color: #e53e3e;
}

.dropdown-menu a.text-warning {
    color: #d69e2e;
}

.dropdown-menu a.text-success {
    color: #38a169;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 0.5rem 0;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-content.large {
    max-width: 800px;
}

.modal-content.extra-large {
    max-width: 1200px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    background: #f7fafc;
    color: #4a5568;
}

.modal-body {
    padding: 2rem;
}

/* نموذج الطبيب */
.doctor-form {
    direction: rtl;
}

.form-tabs {
    margin-bottom: 2rem;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e2e8f0;
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #718096;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-btn:hover {
    color: #4a5568;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    white-space: nowrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-1px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #a0aec0;
    grid-column: 1 / -1;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    color: #e2e8f0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #718096;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 1024px) {
    .doctors-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
}

@media (max-width: 768px) {
    .enhanced-doctors-management {
        padding: 1rem;
    }
    
    .module-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .search-filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        min-width: auto;
    }
    
    .filters-group {
        justify-content: center;
    }
    
    .doctors-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
    
    .modal-body {
        padding: 1rem;
    }
}
