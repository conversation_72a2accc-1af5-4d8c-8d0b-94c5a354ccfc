/**
 * لوحة التحكم الرئيسية المحسنة
 * Enhanced Dashboard System
 *
 * المميزات:
 * - إحصائيات مباشرة وتفاعلية
 * - رسوم بيانية متقدمة
 * - ملخص شامل للأنشطة
 * - تنبيهات ذكية
 * - تحليلات متقدمة
 */

class EnhancedDashboard {
    constructor() {
        this.data = null;
        this.charts = {};
        this.refreshInterval = null;
        this.notifications = [];
        this.initializeDashboard();
    }

    /**
     * تهيئة لوحة التحكم
     */
    initializeDashboard() {
        this.loadData();
        this.setupAutoRefresh();
        this.generateNotifications();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        this.data = JSON.parse(localStorage.getItem('dental_lab_data')) || {
            users: [],
            doctors: [],
            employees: [],
            prostheses: [],
            expenses: [],
            payments: []
        };
    }

    /**
     * إعداد التحديث التلقائي
     */
    setupAutoRefresh() {
        // تحديث البيانات كل 30 ثانية
        this.refreshInterval = setInterval(() => {
            this.refreshDashboard();
        }, 30000);
    }

    /**
     * تحديث لوحة التحكم
     */
    refreshDashboard() {
        this.loadData();
        this.updateStatistics();
        this.updateCharts();
        this.updateRecentActivities();
        this.generateNotifications();
        this.updateNotificationBadge();
    }

    /**
     * إنشاء HTML للوحة التحكم
     */
    generateDashboardHTML() {
        return `
            <div class="enhanced-dashboard">
                <!-- رأس لوحة التحكم -->
                <div class="dashboard-header">
                    <div class="welcome-section">
                        <h1 class="dashboard-title">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم الرئيسية
                        </h1>
                        <p class="dashboard-subtitle">
                            مرحباً ${authSystem.getCurrentUser()?.username || 'المستخدم'}،
                            إليك ملخص شامل لأنشطة المعمل
                        </p>
                    </div>
                    <div class="dashboard-actions">
                        <button class="btn btn-primary" onclick="dashboard.refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-secondary" onclick="dashboard.exportReport()">
                            <i class="fas fa-download"></i>
                            تصدير تقرير
                        </button>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات الرئيسية -->
                <div class="stats-grid">
                    ${this.generateStatsCards()}
                </div>

                <!-- الرسوم البيانية -->
                <div class="charts-section">
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>إحصائيات التركيبات الشهرية</h3>
                                <div class="chart-controls">
                                    <select id="monthly-chart-period">
                                        <option value="6">آخر 6 أشهر</option>
                                        <option value="12" selected>آخر 12 شهر</option>
                                        <option value="24">آخر سنتين</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="monthly-prostheses-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>توزيع أنواع التركيبات</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="prostheses-types-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>الإيرادات مقابل المصروفات</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="revenue-expenses-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>أداء الأطباء</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="doctors-performance-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأنشطة الحديثة والتنبيهات -->
                <div class="activities-section">
                    <div class="activities-grid">
                        <div class="recent-activities-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-clock"></i>
                                    الأنشطة الحديثة
                                </h3>
                                <button class="btn btn-sm btn-outline" onclick="dashboard.viewAllActivities()">
                                    عرض الكل
                                </button>
                            </div>
                            <div class="activities-list" id="recent-activities-list">
                                ${this.generateRecentActivities()}
                            </div>
                        </div>

                        <div class="notifications-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-bell"></i>
                                    التنبيهات والتذكيرات
                                    <span class="notification-badge" id="notification-badge">${this.notifications.length}</span>
                                </h3>
                                <button class="btn btn-sm btn-outline" onclick="dashboard.clearAllNotifications()">
                                    مسح الكل
                                </button>
                            </div>
                            <div class="notifications-list" id="notifications-list">
                                ${this.generateNotificationsList()}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص سريع -->
                <div class="quick-summary-section">
                    <div class="summary-card">
                        <h3>
                            <i class="fas fa-chart-line"></i>
                            ملخص الأداء اليومي
                        </h3>
                        <div class="summary-content">
                            ${this.generateDailySummary()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء بطاقات الإحصائيات
     */
    generateStatsCards() {
        const stats = this.calculateStatistics();

        return `
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-tooth"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.totalProstheses}</div>
                    <div class="stat-label">إجمالي التركيبات</div>
                    <div class="stat-change ${stats.prosthesesChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${stats.prosthesesChange >= 0 ? 'up' : 'down'}"></i>
                        ${Math.abs(stats.prosthesesChange)}% من الشهر الماضي
                    </div>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.totalRevenue.toLocaleString()} ج.م</div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                    <div class="stat-change ${stats.revenueChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${stats.revenueChange >= 0 ? 'up' : 'down'}"></i>
                        ${Math.abs(stats.revenueChange)}% من الشهر الماضي
                    </div>
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.activeDoctors}</div>
                    <div class="stat-label">الأطباء النشطون</div>
                    <div class="stat-change neutral">
                        <i class="fas fa-users"></i>
                        من إجمالي ${stats.totalDoctors} طبيب
                    </div>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.pendingProstheses}</div>
                    <div class="stat-label">تركيبات قيد الانتظار</div>
                    <div class="stat-change ${stats.pendingProstheses > 10 ? 'negative' : 'neutral'}">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${stats.pendingProstheses > 10 ? 'يحتاج متابعة' : 'ضمن المعدل الطبيعي'}
                    </div>
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.overdueProstheses}</div>
                    <div class="stat-label">تركيبات متأخرة</div>
                    <div class="stat-change ${stats.overdueProstheses > 0 ? 'negative' : 'positive'}">
                        <i class="fas fa-${stats.overdueProstheses > 0 ? 'exclamation-circle' : 'check-circle'}"></i>
                        ${stats.overdueProstheses > 0 ? 'يحتاج إجراء فوري' : 'لا توجد متأخرات'}
                    </div>
                </div>
            </div>

            <div class="stat-card secondary">
                <div class="stat-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.completionRate}%</div>
                    <div class="stat-label">معدل الإنجاز</div>
                    <div class="stat-change ${stats.completionRate >= 80 ? 'positive' : stats.completionRate >= 60 ? 'neutral' : 'negative'}">
                        <i class="fas fa-${stats.completionRate >= 80 ? 'thumbs-up' : stats.completionRate >= 60 ? 'minus' : 'thumbs-down'}"></i>
                        ${stats.completionRate >= 80 ? 'ممتاز' : stats.completionRate >= 60 ? 'جيد' : 'يحتاج تحسين'}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حساب الإحصائيات
     */
    calculateStatistics() {
        const prostheses = this.data.prostheses || [];
        const doctors = this.data.doctors || [];
        const payments = this.data.payments || [];
        const expenses = this.data.expenses || [];

        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

        // إحصائيات التركيبات
        const totalProstheses = prostheses.length;
        const thisMonthProstheses = prostheses.filter(p => new Date(p.created_at) >= thisMonth).length;
        const lastMonthProstheses = prostheses.filter(p => {
            const date = new Date(p.created_at);
            return date >= lastMonth && date < thisMonth;
        }).length;

        // إحصائيات الإيرادات
        const totalRevenue = prostheses.reduce((sum, p) => sum + (p.totalPrice || 0), 0);
        const thisMonthRevenue = prostheses
            .filter(p => new Date(p.created_at) >= thisMonth)
            .reduce((sum, p) => sum + (p.totalPrice || 0), 0);
        const lastMonthRevenue = prostheses
            .filter(p => {
                const date = new Date(p.created_at);
                return date >= lastMonth && date < thisMonth;
            })
            .reduce((sum, p) => sum + (p.totalPrice || 0), 0);

        // إحصائيات الأطباء
        const totalDoctors = doctors.length;
        const activeDoctors = doctors.filter(d => {
            return prostheses.some(p => p.doctorId === d.id && new Date(p.created_at) >= thisMonth);
        }).length;

        // إحصائيات الحالة
        const pendingProstheses = prostheses.filter(p => p.status === 'pending').length;
        const completedProstheses = prostheses.filter(p => p.status === 'completed' || p.status === 'delivered').length;
        const overdueProstheses = prostheses.filter(p => {
            return p.status !== 'completed' && p.status !== 'delivered' &&
                   new Date(p.deliveryDate) < now;
        }).length;

        // حساب التغييرات النسبية
        const prosthesesChange = lastMonthProstheses > 0 ?
            ((thisMonthProstheses - lastMonthProstheses) / lastMonthProstheses * 100).toFixed(1) : 0;
        const revenueChange = lastMonthRevenue > 0 ?
            ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100).toFixed(1) : 0;

        // معدل الإنجاز
        const completionRate = totalProstheses > 0 ?
            Math.round((completedProstheses / totalProstheses) * 100) : 0;

        return {
            totalProstheses,
            prosthesesChange: parseFloat(prosthesesChange),
            totalRevenue,
            revenueChange: parseFloat(revenueChange),
            totalDoctors,
            activeDoctors,
            pendingProstheses,
            overdueProstheses,
            completionRate
        };
    }

    /**
     * إنشاء قائمة الأنشطة الحديثة
     */
    generateRecentActivities() {
        const activities = this.getRecentActivities();

        if (activities.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
            `;
        }

        return activities.map(activity => `
            <div class="activity-item ${activity.type}">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                    <div class="activity-time">${this.formatTimeAgo(activity.timestamp)}</div>
                </div>
                ${activity.urgent ? '<div class="activity-urgent"><i class="fas fa-exclamation"></i></div>' : ''}
            </div>
        `).join('');
    }

    /**
     * الحصول على الأنشطة الحديثة
     */
    getRecentActivities() {
        const activities = [];
        const prostheses = this.data.prostheses || [];
        const now = new Date();
        const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        // التركيبات الجديدة
        prostheses
            .filter(p => new Date(p.created_at) >= last24Hours)
            .forEach(p => {
                activities.push({
                    type: 'prosthesis-added',
                    icon: 'fas fa-plus-circle',
                    title: 'تركيبة جديدة',
                    description: `تم إضافة تركيبة ${p.caseId} للمريض ${p.patientName}`,
                    timestamp: p.created_at,
                    urgent: false
                });
            });

        // التركيبات المكتملة
        prostheses
            .filter(p => p.status === 'completed' && new Date(p.updated_at || p.created_at) >= last24Hours)
            .forEach(p => {
                activities.push({
                    type: 'prosthesis-completed',
                    icon: 'fas fa-check-circle',
                    title: 'تركيبة مكتملة',
                    description: `تم إنجاز تركيبة ${p.caseId} للمريض ${p.patientName}`,
                    timestamp: p.updated_at || p.created_at,
                    urgent: false
                });
            });

        // التركيبات المتأخرة
        prostheses
            .filter(p => p.status !== 'completed' && p.status !== 'delivered' && new Date(p.deliveryDate) < now)
            .forEach(p => {
                activities.push({
                    type: 'prosthesis-overdue',
                    icon: 'fas fa-exclamation-triangle',
                    title: 'تركيبة متأخرة',
                    description: `تركيبة ${p.caseId} متأخرة عن موعد التسليم`,
                    timestamp: p.deliveryDate,
                    urgent: true
                });
            });

        // ترتيب حسب الوقت (الأحدث أولاً)
        return activities
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, 10); // أحدث 10 أنشطة
    }

    /**
     * إنشاء التنبيهات
     */
    generateNotifications() {
        this.notifications = [];
        const prostheses = this.data.prostheses || [];
        const now = new Date();
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

        // تنبيهات التسليم
        prostheses.forEach(p => {
            const deliveryDate = new Date(p.deliveryDate);

            if (p.status !== 'completed' && p.status !== 'delivered') {
                if (deliveryDate < now) {
                    // متأخر
                    this.notifications.push({
                        type: 'error',
                        icon: 'fas fa-exclamation-circle',
                        title: 'تركيبة متأخرة',
                        message: `تركيبة ${p.caseId} متأخرة ${this.formatTimeAgo(p.deliveryDate)}`,
                        urgent: true,
                        action: 'view_prosthesis',
                        actionData: p.id
                    });
                } else if (deliveryDate <= tomorrow) {
                    // موعد التسليم غداً
                    this.notifications.push({
                        type: 'warning',
                        icon: 'fas fa-clock',
                        title: 'موعد تسليم قريب',
                        message: `تركيبة ${p.caseId} موعد تسليمها غداً`,
                        urgent: false,
                        action: 'view_prosthesis',
                        actionData: p.id
                    });
                } else if (deliveryDate <= nextWeek) {
                    // موعد التسليم خلال الأسبوع
                    this.notifications.push({
                        type: 'info',
                        icon: 'fas fa-calendar',
                        title: 'موعد تسليم قادم',
                        message: `تركيبة ${p.caseId} موعد تسليمها ${this.formatDate(p.deliveryDate)}`,
                        urgent: false,
                        action: 'view_prosthesis',
                        actionData: p.id
                    });
                }
            }
        });

        // تنبيهات الأداء
        const stats = this.calculateStatistics();
        if (stats.completionRate < 60) {
            this.notifications.push({
                type: 'warning',
                icon: 'fas fa-chart-line',
                title: 'معدل إنجاز منخفض',
                message: `معدل الإنجاز الحالي ${stats.completionRate}% يحتاج تحسين`,
                urgent: false,
                action: 'view_reports',
                actionData: null
            });
        }

        if (stats.pendingProstheses > 20) {
            this.notifications.push({
                type: 'info',
                icon: 'fas fa-tasks',
                title: 'تركيبات قيد الانتظار',
                message: `يوجد ${stats.pendingProstheses} تركيبة قيد الانتظار`,
                urgent: false,
                action: 'view_pending',
                actionData: null
            });
        }

        // ترتيب التنبيهات (العاجل أولاً)
        this.notifications.sort((a, b) => {
            if (a.urgent && !b.urgent) return -1;
            if (!a.urgent && b.urgent) return 1;
            return 0;
        });
    }

    /**
     * إنشاء قائمة التنبيهات
     */
    generateNotificationsList() {
        if (this.notifications.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <p>لا توجد تنبيهات جديدة</p>
                </div>
            `;
        }

        return this.notifications.slice(0, 8).map(notification => `
            <div class="notification-item ${notification.type} ${notification.urgent ? 'urgent' : ''}">
                <div class="notification-icon">
                    <i class="${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                </div>
                ${notification.action ? `
                    <div class="notification-action">
                        <button class="btn btn-sm btn-outline" onclick="dashboard.handleNotificationAction('${notification.action}', ${notification.actionData})">
                            عرض
                        </button>
                    </div>
                ` : ''}
                <div class="notification-close" onclick="dashboard.dismissNotification(${this.notifications.indexOf(notification)})">
                    <i class="fas fa-times"></i>
                </div>
            </div>
        `).join('');
    }

    /**
     * إنشاء الملخص اليومي
     */
    generateDailySummary() {
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const prostheses = this.data.prostheses || [];

        const todayProstheses = prostheses.filter(p => new Date(p.created_at) >= todayStart);
        const todayRevenue = todayProstheses.reduce((sum, p) => sum + (p.totalPrice || 0), 0);
        const todayCompleted = prostheses.filter(p =>
            (p.status === 'completed' || p.status === 'delivered') &&
            new Date(p.updated_at || p.created_at) >= todayStart
        ).length;

        return `
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${todayProstheses.length}</div>
                        <div class="summary-label">تركيبات جديدة اليوم</div>
                    </div>
                </div>

                <div class="summary-item">
                    <div class="summary-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${todayCompleted}</div>
                        <div class="summary-label">تركيبات مكتملة اليوم</div>
                    </div>
                </div>

                <div class="summary-item">
                    <div class="summary-icon">
                        <i class="fas fa-money-bill"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${todayRevenue.toLocaleString()}</div>
                        <div class="summary-label">إيرادات اليوم (ج.م)</div>
                    </div>
                </div>

                <div class="summary-item">
                    <div class="summary-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-number">${todayProstheses.length > 0 ? Math.round((todayCompleted / todayProstheses.length) * 100) : 0}%</div>
                        <div class="summary-label">معدل الإنجاز اليومي</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * تحديث الإحصائيات
     */
    updateStatistics() {
        const statsContainer = document.querySelector('.stats-grid');
        if (statsContainer) {
            statsContainer.innerHTML = this.generateStatsCards();
        }
    }

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts() {
        this.initializeCharts();
    }

    /**
     * تحديث الأنشطة الحديثة
     */
    updateRecentActivities() {
        const activitiesList = document.getElementById('recent-activities-list');
        if (activitiesList) {
            activitiesList.innerHTML = this.generateRecentActivities();
        }
    }

    /**
     * تحديث شارة التنبيهات
     */
    updateNotificationBadge() {
        const badge = document.getElementById('notification-badge');
        if (badge) {
            badge.textContent = this.notifications.length;
            badge.style.display = this.notifications.length > 0 ? 'inline' : 'none';
        }

        const notificationsList = document.getElementById('notifications-list');
        if (notificationsList) {
            notificationsList.innerHTML = this.generateNotificationsList();
        }
    }

    /**
     * تهيئة الرسوم البيانية
     */
    initializeCharts() {
        // التحقق من وجود مكتبة Chart.js
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js library not loaded');
            return;
        }

        this.initMonthlyProsthesesChart();
        this.initProsthesesTypesChart();
        this.initRevenueExpensesChart();
        this.initDoctorsPerformanceChart();
    }

    /**
     * رسم بياني للتركيبات الشهرية
     */
    initMonthlyProsthesesChart() {
        const canvas = document.getElementById('monthly-prostheses-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.getMonthlyProsthesesData();

        if (this.charts.monthlyProstheses) {
            this.charts.monthlyProstheses.destroy();
        }

        this.charts.monthlyProstheses = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'عدد التركيبات',
                    data: data.values,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    /**
     * رسم بياني لأنواع التركيبات
     */
    initProsthesesTypesChart() {
        const canvas = document.getElementById('prostheses-types-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.getProsthesesTypesData();

        if (this.charts.prosthesesTypes) {
            this.charts.prosthesesTypes.destroy();
        }

        this.charts.prosthesesTypes = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe',
                        '#43e97b'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    /**
     * الحصول على بيانات التركيبات الشهرية
     */
    getMonthlyProsthesesData() {
        const prostheses = this.data.prostheses || [];
        const months = [];
        const values = [];

        // إنشاء آخر 12 شهر
        for (let i = 11; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthName = date.toLocaleDateString('ar-EG', { month: 'short', year: 'numeric' });

            months.push(monthName);

            const monthProstheses = prostheses.filter(p => {
                const prosthesisDate = new Date(p.created_at);
                const prosthesisKey = `${prosthesisDate.getFullYear()}-${String(prosthesisDate.getMonth() + 1).padStart(2, '0')}`;
                return prosthesisKey === monthKey;
            }).length;

            values.push(monthProstheses);
        }

        return { labels: months, values: values };
    }

    /**
     * الحصول على بيانات أنواع التركيبات
     */
    getProsthesesTypesData() {
        const prostheses = this.data.prostheses || [];
        const typeCounts = {};

        prostheses.forEach(p => {
            const type = this.getProsthesisTypeLabel(p.type);
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        });

        return {
            labels: Object.keys(typeCounts),
            values: Object.values(typeCounts)
        };
    }

    /**
     * الحصول على تسمية نوع التركيبة
     */
    getProsthesisTypeLabel(type) {
        const typeLabels = {
            'crown': 'تاج',
            'bridge': 'جسر',
            'partial_denture': 'طقم جزئي',
            'complete_denture': 'طقم كامل',
            'implant': 'زراعة',
            'veneer': 'عدسة',
            'orthodontic': 'تقويم'
        };

        return typeLabels[type] || type || 'غير محدد';
    }

    /**
     * تنسيق الوقت المنقضي
     */
    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'منذ لحظات';
        if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
        if (diffInSeconds < 2592000) return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;

        return date.toLocaleDateString('ar-EG');
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    /**
     * معالجة إجراء التنبيه
     */
    handleNotificationAction(action, data) {
        switch (action) {
            case 'view_prosthesis':
                // الانتقال لعرض التركيبة
                if (typeof showProsthesisModule === 'function') {
                    showProsthesisModule('list');
                }
                break;
            case 'view_reports':
                // الانتقال للتقارير
                if (typeof showReportsModule === 'function') {
                    showReportsModule();
                }
                break;
            case 'view_pending':
                // عرض التركيبات المعلقة
                if (typeof showProsthesisModule === 'function') {
                    showProsthesisModule('list');
                    // تطبيق فلتر للمعلقة
                }
                break;
        }
    }

    /**
     * إزالة تنبيه
     */
    dismissNotification(index) {
        this.notifications.splice(index, 1);
        this.updateNotificationBadge();
    }

    /**
     * مسح جميع التنبيهات
     */
    clearAllNotifications() {
        this.notifications = [];
        this.updateNotificationBadge();
    }

    /**
     * عرض جميع الأنشطة
     */
    viewAllActivities() {
        // يمكن فتح نافذة منبثقة أو الانتقال لصفحة منفصلة
        alert('ميزة عرض جميع الأنشطة ستكون متاحة قريباً');
    }

    /**
     * تصدير تقرير
     */
    exportReport() {
        const stats = this.calculateStatistics();
        const reportData = {
            generatedAt: new Date().toISOString(),
            statistics: stats,
            recentActivities: this.getRecentActivities(),
            notifications: this.notifications
        };

        const dataStr = JSON.stringify(reportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `dashboard-report-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    /**
     * تدمير لوحة التحكم
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });

        this.charts = {};
    }
}

// إنشاء مثيل عام من لوحة التحكم
const dashboard = new EnhancedDashboard();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedDashboard, dashboard };
}