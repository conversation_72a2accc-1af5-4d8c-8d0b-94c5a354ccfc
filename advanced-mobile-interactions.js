/**
 * نظام التفاعلات المتقدمة للموبايل
 * Advanced Mobile Interactions System
 *
 * يوفر تفاعلات متقدمة للأجهزة المحمولة مع دعم الإيماءات المتعددة
 */

class AdvancedMobileInteractions {
    constructor() {
        this.isEnabled = false;
        this.gestures = {};
        this.touchPoints = new Map();
        this.activeGestures = new Set();
        this.settings = {
            swipeThreshold: 50,
            pinchThreshold: 0.1,
            rotateThreshold: 15,
            longPressDelay: 500,
            doubleTapDelay: 300,
            maxTouchPoints: 10
        };

        this.initialize();
    }

    /**
     * تهيئة النظام
     */
    initialize() {
        if (!this.isTouchDevice()) {
            console.log('Touch device not detected, mobile interactions disabled');
            return;
        }

        this.setupEventListeners();
        this.setupGestureRecognizers();
        this.setupHapticFeedback();
        this.setupAccessibility();
        this.isEnabled = true;

        console.log('Advanced mobile interactions initialized');
    }

    /**
     * التحقق من دعم اللمس
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // أحداث اللمس الأساسية
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        document.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: false });

        // أحداث الإيماءات المدمجة
        document.addEventListener('gesturestart', this.handleGestureStart.bind(this), { passive: false });
        document.addEventListener('gesturechange', this.handleGestureChange.bind(this), { passive: false });
        document.addEventListener('gestureend', this.handleGestureEnd.bind(this), { passive: false });

        // أحداث تغيير الاتجاه
        window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));

        // أحداث الحركة
        if (window.DeviceMotionEvent) {
            window.addEventListener('devicemotion', this.handleDeviceMotion.bind(this));
        }

        if (window.DeviceOrientationEvent) {
            window.addEventListener('deviceorientation', this.handleDeviceOrientation.bind(this));
        }
    }

    /**
     * إعداد مُعرِّفات الإيماءات
     */
    setupGestureRecognizers() {
        this.recognizers = {
            tap: new TapRecognizer(this.settings),
            doubleTap: new DoubleTapRecognizer(this.settings),
            longPress: new LongPressRecognizer(this.settings),
            swipe: new SwipeRecognizer(this.settings),
            pinch: new PinchRecognizer(this.settings),
            rotate: new RotateRecognizer(this.settings),
            pan: new PanRecognizer(this.settings)
        };
    }

    /**
     * إعداد الاهتزاز التفاعلي
     */
    setupHapticFeedback() {
        this.haptic = {
            enabled: 'vibrate' in navigator,
            patterns: {
                tap: [10],
                success: [100, 50, 100],
                error: [200, 100, 200],
                warning: [150],
                notification: [50, 50, 50]
            }
        };
    }

    /**
     * إعداد إمكانية الوصول
     */
    setupAccessibility() {
        // دعم قارئ الشاشة
        this.announcer = document.createElement('div');
        this.announcer.setAttribute('aria-live', 'polite');
        this.announcer.setAttribute('aria-atomic', 'true');
        this.announcer.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(this.announcer);

        // إعدادات إمكانية الوصول
        this.accessibility = {
            announceGestures: true,
            largeTargets: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
            highContrast: window.matchMedia('(prefers-contrast: high)').matches,
            reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
        };
    }

    /**
     * معالجة بداية اللمس
     */
    handleTouchStart(event) {
        const touches = Array.from(event.touches);

        touches.forEach(touch => {
            const touchPoint = {
                id: touch.identifier,
                startX: touch.clientX,
                startY: touch.clientY,
                currentX: touch.clientX,
                currentY: touch.clientY,
                startTime: Date.now(),
                element: document.elementFromPoint(touch.clientX, touch.clientY)
            };

            this.touchPoints.set(touch.identifier, touchPoint);
        });

        // تشغيل مُعرِّفات الإيماءات
        Object.values(this.recognizers).forEach(recognizer => {
            recognizer.onTouchStart(touches, this.touchPoints);
        });

        // منع التمرير إذا كان هناك أكثر من لمسة واحدة
        if (touches.length > 1) {
            event.preventDefault();
        }
    }

    /**
     * معالجة حركة اللمس
     */
    handleTouchMove(event) {
        const touches = Array.from(event.touches);

        touches.forEach(touch => {
            const touchPoint = this.touchPoints.get(touch.identifier);
            if (touchPoint) {
                touchPoint.currentX = touch.clientX;
                touchPoint.currentY = touch.clientY;
            }
        });

        // تشغيل مُعرِّفات الإيماءات
        Object.values(this.recognizers).forEach(recognizer => {
            recognizer.onTouchMove(touches, this.touchPoints);
        });

        // منع التمرير الافتراضي للإيماءات المتعددة
        if (touches.length > 1 || this.activeGestures.size > 0) {
            event.preventDefault();
        }
    }

    /**
     * معالجة نهاية اللمس
     */
    handleTouchEnd(event) {
        const touches = Array.from(event.changedTouches);

        // تشغيل مُعرِّفات الإيماءات
        Object.values(this.recognizers).forEach(recognizer => {
            recognizer.onTouchEnd(touches, this.touchPoints);
        });

        // إزالة نقاط اللمس المنتهية
        touches.forEach(touch => {
            this.touchPoints.delete(touch.identifier);
        });
    }

    /**
     * معالجة إلغاء اللمس
     */
    handleTouchCancel(event) {
        const touches = Array.from(event.changedTouches);

        // تشغيل مُعرِّفات الإيماءات
        Object.values(this.recognizers).forEach(recognizer => {
            recognizer.onTouchCancel(touches, this.touchPoints);
        });

        // إزالة نقاط اللمس الملغية
        touches.forEach(touch => {
            this.touchPoints.delete(touch.identifier);
        });
    }

    /**
     * معالجة بداية الإيماءة
     */
    handleGestureStart(event) {
        event.preventDefault();
        this.activeGestures.add('native-gesture');
    }

    /**
     * معالجة تغيير الإيماءة
     */
    handleGestureChange(event) {
        event.preventDefault();

        // إرسال أحداث مخصصة للتكبير والدوران
        if (Math.abs(event.scale - 1) > this.settings.pinchThreshold) {
            this.dispatchGestureEvent('pinch', {
                scale: event.scale,
                center: { x: event.clientX, y: event.clientY }
            });
        }

        if (Math.abs(event.rotation) > this.settings.rotateThreshold) {
            this.dispatchGestureEvent('rotate', {
                rotation: event.rotation,
                center: { x: event.clientX, y: event.clientY }
            });
        }
    }

    /**
     * معالجة نهاية الإيماءة
     */
    handleGestureEnd(event) {
        event.preventDefault();
        this.activeGestures.delete('native-gesture');
    }

    /**
     * معالجة تغيير الاتجاه
     */
    handleOrientationChange() {
        setTimeout(() => {
            const orientation = screen.orientation || {};
            this.dispatchGestureEvent('orientationchange', {
                angle: orientation.angle || window.orientation || 0,
                type: orientation.type || (window.innerWidth > window.innerHeight ? 'landscape' : 'portrait')
            });
        }, 100);
    }

    /**
     * معالجة حركة الجهاز
     */
    handleDeviceMotion(event) {
        const acceleration = event.acceleration;
        const rotationRate = event.rotationRate;

        if (acceleration) {
            // كشف الهز
            const totalAcceleration = Math.sqrt(
                acceleration.x * acceleration.x +
                acceleration.y * acceleration.y +
                acceleration.z * acceleration.z
            );

            if (totalAcceleration > 15) {
                this.dispatchGestureEvent('shake', {
                    acceleration: totalAcceleration,
                    direction: this.getShakeDirection(acceleration)
                });
            }
        }
    }

    /**
     * معالجة اتجاه الجهاز
     */
    handleDeviceOrientation(event) {
        this.dispatchGestureEvent('deviceorientation', {
            alpha: event.alpha, // الدوران حول المحور Z
            beta: event.beta,   // الدوران حول المحور X
            gamma: event.gamma  // الدوران حول المحور Y
        });
    }

    /**
     * إرسال حدث إيماءة مخصص
     */
    dispatchGestureEvent(type, detail) {
        const event = new CustomEvent(`gesture:${type}`, {
            detail: detail,
            bubbles: true,
            cancelable: true
        });

        document.dispatchEvent(event);

        // تشغيل الاهتزاز التفاعلي
        this.triggerHapticFeedback(type);

        // الإعلان للمستخدمين الذين يستخدمون قارئ الشاشة
        if (this.accessibility.announceGestures) {
            this.announceGesture(type, detail);
        }
    }

    /**
     * تشغيل الاهتزاز التفاعلي
     */
    triggerHapticFeedback(type) {
        if (!this.haptic.enabled) return;

        const pattern = this.haptic.patterns[type] || this.haptic.patterns.tap;
        navigator.vibrate(pattern);
    }

    /**
     * الإعلان عن الإيماءة
     */
    announceGesture(type, detail) {
        const messages = {
            tap: 'تم النقر',
            doubleTap: 'نقر مزدوج',
            longPress: 'ضغط طويل',
            swipe: `سحب إلى ${this.getDirectionText(detail.direction)}`,
            pinch: detail.scale > 1 ? 'تكبير' : 'تصغير',
            rotate: 'دوران',
            shake: 'هز الجهاز'
        };

        const message = messages[type] || `إيماءة ${type}`;
        this.announcer.textContent = message;
    }

    /**
     * الحصول على نص الاتجاه
     */
    getDirectionText(direction) {
        const directions = {
            up: 'الأعلى',
            down: 'الأسفل',
            left: 'اليسار',
            right: 'اليمين'
        };
        return directions[direction] || direction;
    }

    /**
     * الحصول على اتجاه الهز
     */
    getShakeDirection(acceleration) {
        const absX = Math.abs(acceleration.x);
        const absY = Math.abs(acceleration.y);
        const absZ = Math.abs(acceleration.z);

        if (absX > absY && absX > absZ) {
            return acceleration.x > 0 ? 'right' : 'left';
        } else if (absY > absZ) {
            return acceleration.y > 0 ? 'up' : 'down';
        } else {
            return acceleration.z > 0 ? 'forward' : 'backward';
        }
    }

    /**
     * تسجيل معالج إيماءة مخصص
     */
    registerGestureHandler(gestureType, handler) {
        document.addEventListener(`gesture:${gestureType}`, handler);
    }

    /**
     * إلغاء تسجيل معالج إيماءة
     */
    unregisterGestureHandler(gestureType, handler) {
        document.removeEventListener(`gesture:${gestureType}`, handler);
    }

    /**
     * تفعيل/إلغاء تفعيل الإيماءات
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;

        if (enabled) {
            this.setupEventListeners();
        } else {
            this.removeEventListeners();
        }
    }

    /**
     * إزالة مستمعي الأحداث
     */
    removeEventListeners() {
        document.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchmove', this.handleTouchMove);
        document.removeEventListener('touchend', this.handleTouchEnd);
        document.removeEventListener('touchcancel', this.handleTouchCancel);
        document.removeEventListener('gesturestart', this.handleGestureStart);
        document.removeEventListener('gesturechange', this.handleGestureChange);
        document.removeEventListener('gestureend', this.handleGestureEnd);
        window.removeEventListener('orientationchange', this.handleOrientationChange);
        window.removeEventListener('devicemotion', this.handleDeviceMotion);
        window.removeEventListener('deviceorientation', this.handleDeviceOrientation);
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };

        // تحديث إعدادات المُعرِّفات
        Object.values(this.recognizers).forEach(recognizer => {
            recognizer.updateSettings(this.settings);
        });
    }

    /**
     * الحصول على معلومات اللمس الحالية
     */
    getTouchInfo() {
        return {
            activeTouches: this.touchPoints.size,
            activeGestures: Array.from(this.activeGestures),
            isEnabled: this.isEnabled
        };
    }
}

/**
 * مُعرِّف النقر
 */
class TapRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.tapTimeout = null;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 1) {
            this.startTime = Date.now();
            this.startTouch = touches[0];
        }
    }

    onTouchMove(touches, touchPoints) {
        // إلغاء النقر إذا تحرك الإصبع كثيراً
        if (touches.length === 1 && this.startTouch) {
            const touch = touches[0];
            const distance = Math.sqrt(
                Math.pow(touch.clientX - this.startTouch.clientX, 2) +
                Math.pow(touch.clientY - this.startTouch.clientY, 2)
            );

            if (distance > 10) {
                this.startTouch = null;
            }
        }
    }

    onTouchEnd(touches, touchPoints) {
        if (touches.length === 1 && this.startTouch) {
            const duration = Date.now() - this.startTime;

            if (duration < 200) {
                const touch = touches[0];
                document.dispatchEvent(new CustomEvent('gesture:tap', {
                    detail: {
                        x: touch.clientX,
                        y: touch.clientY,
                        target: document.elementFromPoint(touch.clientX, touch.clientY)
                    }
                }));
            }
        }

        this.startTouch = null;
    }

    onTouchCancel() {
        this.startTouch = null;
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

/**
 * مُعرِّف النقر المزدوج
 */
class DoubleTapRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.lastTap = null;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 1) {
            this.currentTap = {
                time: Date.now(),
                x: touches[0].clientX,
                y: touches[0].clientY
            };
        }
    }

    onTouchMove() {
        // لا حاجة لمعالجة الحركة في النقر المزدوج
    }

    onTouchEnd(touches, touchPoints) {
        if (touches.length === 1 && this.currentTap) {
            if (this.lastTap) {
                const timeDiff = this.currentTap.time - this.lastTap.time;
                const distance = Math.sqrt(
                    Math.pow(this.currentTap.x - this.lastTap.x, 2) +
                    Math.pow(this.currentTap.y - this.lastTap.y, 2)
                );

                if (timeDiff < this.settings.doubleTapDelay && distance < 50) {
                    document.dispatchEvent(new CustomEvent('gesture:doubleTap', {
                        detail: {
                            x: this.currentTap.x,
                            y: this.currentTap.y,
                            target: document.elementFromPoint(this.currentTap.x, this.currentTap.y)
                        }
                    }));

                    this.lastTap = null;
                    this.currentTap = null;
                    return;
                }
            }

            this.lastTap = this.currentTap;
            this.currentTap = null;
        }
    }

    onTouchCancel() {
        this.currentTap = null;
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

/**
 * مُعرِّف الضغط الطويل
 */
class LongPressRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.pressTimeout = null;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 1) {
            const touch = touches[0];

            this.pressTimeout = setTimeout(() => {
                document.dispatchEvent(new CustomEvent('gesture:longPress', {
                    detail: {
                        x: touch.clientX,
                        y: touch.clientY,
                        target: document.elementFromPoint(touch.clientX, touch.clientY)
                    }
                }));
            }, this.settings.longPressDelay);
        }
    }

    onTouchMove(touches, touchPoints) {
        // إلغاء الضغط الطويل عند الحركة
        if (this.pressTimeout) {
            clearTimeout(this.pressTimeout);
            this.pressTimeout = null;
        }
    }

    onTouchEnd() {
        if (this.pressTimeout) {
            clearTimeout(this.pressTimeout);
            this.pressTimeout = null;
        }
    }

    onTouchCancel() {
        if (this.pressTimeout) {
            clearTimeout(this.pressTimeout);
            this.pressTimeout = null;
        }
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

/**
 * مُعرِّف السحب
 */
class SwipeRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.startTouch = null;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 1) {
            this.startTouch = {
                x: touches[0].clientX,
                y: touches[0].clientY,
                time: Date.now()
            };
        }
    }

    onTouchMove() {
        // لا حاجة لمعالجة الحركة في السحب
    }

    onTouchEnd(touches, touchPoints) {
        if (touches.length === 1 && this.startTouch) {
            const touch = touches[0];
            const deltaX = touch.clientX - this.startTouch.x;
            const deltaY = touch.clientY - this.startTouch.y;
            const deltaTime = Date.now() - this.startTouch.time;

            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const velocity = distance / deltaTime;

            if (distance > this.settings.swipeThreshold && deltaTime < 300) {
                let direction;

                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                    direction = deltaX > 0 ? 'right' : 'left';
                } else {
                    direction = deltaY > 0 ? 'down' : 'up';
                }

                document.dispatchEvent(new CustomEvent('gesture:swipe', {
                    detail: {
                        direction: direction,
                        distance: distance,
                        velocity: velocity,
                        deltaX: deltaX,
                        deltaY: deltaY,
                        startX: this.startTouch.x,
                        startY: this.startTouch.y,
                        endX: touch.clientX,
                        endY: touch.clientY
                    }
                }));
            }
        }

        this.startTouch = null;
    }

    onTouchCancel() {
        this.startTouch = null;
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

/**
 * مُعرِّف القرص (التكبير/التصغير)
 */
class PinchRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.initialDistance = 0;
        this.currentScale = 1;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 2) {
            this.initialDistance = this.getDistance(touches[0], touches[1]);
            this.currentScale = 1;
        }
    }

    onTouchMove(touches, touchPoints) {
        if (touches.length === 2) {
            const currentDistance = this.getDistance(touches[0], touches[1]);
            const scale = currentDistance / this.initialDistance;

            if (Math.abs(scale - this.currentScale) > this.settings.pinchThreshold) {
                const center = this.getCenter(touches[0], touches[1]);

                document.dispatchEvent(new CustomEvent('gesture:pinch', {
                    detail: {
                        scale: scale,
                        delta: scale - this.currentScale,
                        center: center,
                        distance: currentDistance
                    }
                }));

                this.currentScale = scale;
            }
        }
    }

    onTouchEnd(touches, touchPoints) {
        if (touchPoints.size < 2) {
            this.initialDistance = 0;
            this.currentScale = 1;
        }
    }

    onTouchCancel() {
        this.initialDistance = 0;
        this.currentScale = 1;
    }

    getDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    getCenter(touch1, touch2) {
        return {
            x: (touch1.clientX + touch2.clientX) / 2,
            y: (touch1.clientY + touch2.clientY) / 2
        };
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

/**
 * مُعرِّف الدوران
 */
class RotateRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.initialAngle = 0;
        this.currentRotation = 0;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 2) {
            this.initialAngle = this.getAngle(touches[0], touches[1]);
            this.currentRotation = 0;
        }
    }

    onTouchMove(touches, touchPoints) {
        if (touches.length === 2) {
            const currentAngle = this.getAngle(touches[0], touches[1]);
            const rotation = currentAngle - this.initialAngle;

            if (Math.abs(rotation - this.currentRotation) > this.settings.rotateThreshold) {
                const center = this.getCenter(touches[0], touches[1]);

                document.dispatchEvent(new CustomEvent('gesture:rotate', {
                    detail: {
                        rotation: rotation,
                        delta: rotation - this.currentRotation,
                        center: center,
                        angle: currentAngle
                    }
                }));

                this.currentRotation = rotation;
            }
        }
    }

    onTouchEnd(touches, touchPoints) {
        if (touchPoints.size < 2) {
            this.initialAngle = 0;
            this.currentRotation = 0;
        }
    }

    onTouchCancel() {
        this.initialAngle = 0;
        this.currentRotation = 0;
    }

    getAngle(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.atan2(dy, dx) * 180 / Math.PI;
    }

    getCenter(touch1, touch2) {
        return {
            x: (touch1.clientX + touch2.clientX) / 2,
            y: (touch1.clientY + touch2.clientY) / 2
        };
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

/**
 * مُعرِّف السحب والإفلات
 */
class PanRecognizer {
    constructor(settings) {
        this.settings = settings;
        this.isPanning = false;
        this.startTouch = null;
    }

    onTouchStart(touches, touchPoints) {
        if (touches.length === 1) {
            this.startTouch = {
                x: touches[0].clientX,
                y: touches[0].clientY,
                time: Date.now()
            };
            this.isPanning = false;
        }
    }

    onTouchMove(touches, touchPoints) {
        if (touches.length === 1 && this.startTouch) {
            const touch = touches[0];
            const deltaX = touch.clientX - this.startTouch.x;
            const deltaY = touch.clientY - this.startTouch.y;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (!this.isPanning && distance > 10) {
                this.isPanning = true;

                document.dispatchEvent(new CustomEvent('gesture:panStart', {
                    detail: {
                        x: this.startTouch.x,
                        y: this.startTouch.y,
                        target: document.elementFromPoint(this.startTouch.x, this.startTouch.y)
                    }
                }));
            }

            if (this.isPanning) {
                document.dispatchEvent(new CustomEvent('gesture:pan', {
                    detail: {
                        deltaX: deltaX,
                        deltaY: deltaY,
                        currentX: touch.clientX,
                        currentY: touch.clientY,
                        startX: this.startTouch.x,
                        startY: this.startTouch.y
                    }
                }));
            }
        }
    }

    onTouchEnd(touches, touchPoints) {
        if (this.isPanning) {
            const touch = touches[0];

            document.dispatchEvent(new CustomEvent('gesture:panEnd', {
                detail: {
                    endX: touch.clientX,
                    endY: touch.clientY,
                    startX: this.startTouch.x,
                    startY: this.startTouch.y
                }
            }));
        }

        this.isPanning = false;
        this.startTouch = null;
    }

    onTouchCancel() {
        if (this.isPanning) {
            document.dispatchEvent(new CustomEvent('gesture:panCancel', {
                detail: {
                    startX: this.startTouch.x,
                    startY: this.startTouch.y
                }
            }));
        }

        this.isPanning = false;
        this.startTouch = null;
    }

    updateSettings(settings) {
        this.settings = settings;
    }
}

// إنشاء مثيل عام من نظام التفاعلات المتقدمة
const mobileInteractions = new AdvancedMobileInteractions();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        AdvancedMobileInteractions,
        mobileInteractions,
        TapRecognizer,
        DoubleTapRecognizer,
        LongPressRecognizer,
        SwipeRecognizer,
        PinchRecognizer,
        RotateRecognizer,
        PanRecognizer
    };
}