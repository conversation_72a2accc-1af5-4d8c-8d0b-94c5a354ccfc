# 🦷 نظام معمل الأسنان المتطور
## Enhanced Dental Laboratory Management System

نظام إدارة شامل ومتطور لمعامل الأسنان يوفر حلولاً متكاملة لإدارة جميع جوانب العمل في معمل الأسنان الحديث.

---

## 🌟 المميزات الرئيسية

### 🔐 نظام المصادقة والأمان المتقدم
- تسجيل دخول آمن مع تشفير كلمات المرور
- إدارة الجلسات والصلاحيات
- مستويات أمان متعددة
- نظام استرداد كلمة المرور

### 📊 لوحة التحكم التفاعلية
- إحصائيات مباشرة ومحدثة
- رسوم بيانية تفاعلية
- ملخص شامل للأنشطة اليومية
- مؤشرات الأداء الرئيسية

### 👨‍⚕️ إدارة الأطباء المتقدمة
- ملفات شخصية مفصلة للأطباء
- تاريخ العمل والتعاملات
- إعدادات الأسعار المخصصة
- نظام التقييم والمراجعات

### 👥 إدارة الموظفين الشاملة
- نظام الرواتب والعمولات
- تتبع الحضور والانصراف
- تقييمات الأداء
- إدارة الإجازات والمكافآت

### 🦷 نظام التركيبات المتطور
- مخطط الأسنان التفاعلي المطور
- تتبع مراحل الإنتاج
- نظام التصوير والمرفقات
- إدارة الجودة والفحص

### 💰 النظام المالي المتكامل
- إدارة الفواتير والمدفوعات
- نظام المحاسبة المزدوجة
- تتبع الإيرادات والمصروفات
- إدارة الضرائب والخصومات

### 📈 نظام التقارير والإحصائيات
- تقارير تفاعلية مع رسوم بيانية
- تصدير متعدد الصيغ (PDF, Excel, CSV)
- تحليلات ذكية ومؤشرات الأداء
- تقارير مجدولة وتلقائية

---

## 🚀 التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - البرمجة التفاعلية
- **Chart.js** - الرسوم البيانية
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

### Backend (مستقبلي)
- **Node.js** - خادم التطبيق
- **Express.js** - إطار العمل
- **MongoDB** - قاعدة البيانات
- **JWT** - المصادقة
- **Multer** - رفع الملفات

### التخزين الحالي
- **LocalStorage** - تخزين البيانات محلياً
- **JSON** - تنسيق البيانات

---

## 📁 هيكل المشروع

```
enhanced-dental-lab-system/
├── enhanced-dental-lab-system.html    # الملف الرئيسي
├── enhanced-auth-system.js            # نظام المصادقة
├── enhanced-auth-system.css           # أنماط المصادقة
├── enhanced-dashboard.js              # لوحة التحكم
├── enhanced-dashboard.css             # أنماط لوحة التحكم
├── enhanced-doctors-management.js     # إدارة الأطباء
├── enhanced-doctors-management.css    # أنماط إدارة الأطباء
├── enhanced-employees-management.js   # إدارة الموظفين
├── enhanced-employees-management.css  # أنماط إدارة الموظفين
├── enhanced-prostheses-system.js      # نظام التركيبات
├── enhanced-prostheses-system.css     # أنماط نظام التركيبات
├── enhanced-financial-system.js       # النظام المالي
├── enhanced-financial-system.css      # أنماط النظام المالي
├── enhanced-reports-system.js         # نظام التقارير
├── enhanced-reports-system.css        # أنماط نظام التقارير
└── README.md                          # هذا الملف
```

---

## 🛠️ التثبيت والتشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- خادم ويب محلي (اختياري)

### خطوات التشغيل

1. **تحميل الملفات**
   ```bash
   git clone https://github.com/your-repo/enhanced-dental-lab-system.git
   cd enhanced-dental-lab-system
   ```

2. **تشغيل النظام**
   - افتح ملف `enhanced-dental-lab-system.html` في المتصفح مباشرة
   - أو استخدم خادم ويب محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx http-server
   
   # باستخدام PHP
   php -S localhost:8000
   ```

3. **الوصول للنظام**
   - افتح المتصفح وانتقل إلى `http://localhost:8000`
   - أو افتح الملف مباشرة في المتصفح

---

## 👤 بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

---

## 📱 التوافق والاستجابة

النظام متوافق مع جميع الأجهزة والشاشات:
- 🖥️ أجهزة الكمبيوتر المكتبية
- 💻 أجهزة الكمبيوتر المحمولة
- 📱 الهواتف الذكية
- 📟 الأجهزة اللوحية

---

## 🔧 التخصيص والتطوير

### إضافة وحدة جديدة

1. **إنشاء ملف JavaScript**
   ```javascript
   class NewModule {
       constructor() {
           // تهيئة الوحدة
       }
       
       generateHTML() {
           return `<!-- HTML للوحدة -->`;
       }
   }
   
   const newModule = new NewModule();
   ```

2. **إنشاء ملف CSS**
   ```css
   .new-module {
       /* أنماط الوحدة */
   }
   ```

3. **إضافة الوحدة للنظام الرئيسي**
   - أضف رابط في الشريط الجانبي
   - أضف حاوي المحتوى
   - أضف حالة في دالة `loadModule()`

### تخصيص الألوان

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
}
```

---

## 📊 البيانات والتخزين

### هيكل البيانات
```javascript
{
    "doctors": [],
    "employees": [],
    "prostheses": [],
    "invoices": [],
    "payments": [],
    "expenses": [],
    "attendance": [],
    "reports": []
}
```

### النسخ الاحتياطي
- يتم حفظ البيانات تلقائياً في LocalStorage
- يمكن تصدير البيانات بصيغة JSON
- يمكن استيراد البيانات من ملفات JSON

---

## 🔮 التطويرات المستقبلية

### المرحلة الثانية
- [ ] تطوير Backend بـ Node.js
- [ ] قاعدة بيانات MongoDB
- [ ] نظام المصادقة JWT
- [ ] API RESTful

### المرحلة الثالثة
- [ ] تطبيق الهاتف المحمول
- [ ] نظام الإشعارات المباشرة
- [ ] التكامل مع أنظمة خارجية
- [ ] الذكاء الاصطناعي للتنبؤات

### المرحلة الرابعة
- [ ] نظام إدارة المخزون
- [ ] التكامل مع أجهزة المعمل
- [ ] نظام إدارة العملاء
- [ ] التجارة الإلكترونية

---

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://dentallab.com
- **الدعم الفني**: https://support.dentallab.com

---

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام والمجتمع المفتوح المصدر.

---

**© 2024 نظام معمل الأسنان المتطور. جميع الحقوق محفوظة.**
