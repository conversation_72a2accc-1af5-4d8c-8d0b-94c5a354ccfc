/**
 * نظام التصميم التكيفي المتقدم
 * Advanced Adaptive Design System
 * 
 * يتكيف تلقائياً مع نوع الجهاز وحجم الشاشة وتفضيلات المستخدم
 */

/* متغيرات CSS الديناميكية */
:root {
    /* نقاط الكسر المتقدمة */
    --breakpoint-xs: 320px;
    --breakpoint-sm: 480px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
    
    /* الشبكة التكيفية */
    --grid-columns: 12;
    --grid-gap: 1rem;
    --container-padding: 1rem;
    
    /* الطباعة التكيفية */
    --font-scale-ratio: 1.25;
    --font-size-base: clamp(14px, 2.5vw, 16px);
    --line-height-base: 1.6;
    
    /* المسافات التكيفية */
    --spacing-unit: clamp(0.25rem, 1vw, 0.5rem);
    --spacing-xs: calc(var(--spacing-unit) * 0.5);
    --spacing-sm: var(--spacing-unit);
    --spacing-md: calc(var(--spacing-unit) * 2);
    --spacing-lg: calc(var(--spacing-unit) * 3);
    --spacing-xl: calc(var(--spacing-unit) * 4);
    --spacing-2xl: calc(var(--spacing-unit) * 6);
    
    /* الألوان التكيفية */
    --color-primary: #667eea;
    --color-primary-light: #a5b4fc;
    --color-primary-dark: #4338ca;
    --color-secondary: #764ba2;
    --color-accent: #f093fb;
    
    /* ألوان النظام */
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;
    
    /* ألوان الخلفية التكيفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* ألوان النص التكيفية */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;
    
    /* الظلال التكيفية */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    
    /* نصف القطر التكيفي */
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    --radius-full: 9999px;
    
    /* الانتقالات التكيفية */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* الحدود التكيفية */
    --border-width: 1px;
    --border-color: #e2e8f0;
    --border-radius: var(--radius-md);
    
    /* الشفافية */
    --opacity-disabled: 0.5;
    --opacity-hover: 0.8;
    --opacity-focus: 0.9;
}

/* تكيف مع النظام الداكن */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --bg-overlay: rgba(0, 0, 0, 0.8);
        
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-tertiary: #94a3b8;
        --text-inverse: #1e293b;
        
        --border-color: #475569;
        
        --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.3);
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
        --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.4);
        --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.6);
    }
}

/* تكيف مع تفضيل التباين العالي */
@media (prefers-contrast: high) {
    :root {
        --color-primary: #000080;
        --color-secondary: #800080;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
        --border-width: 2px;
    }
}

/* تكيف مع تفضيل تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0ms;
        --transition-normal: 0ms;
        --transition-slow: 0ms;
    }
    
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* الإعدادات الأساسية */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    direction: rtl;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* نظام الشبكة التكيفي */
.container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--container-padding);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(var(--grid-gap) * -0.5);
}

.col {
    flex: 1;
    padding: 0 calc(var(--grid-gap) * 0.5);
    min-width: 0;
}

/* أعمدة الشبكة التكيفية */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* نظام الطباعة التكيفي */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h2 { font-size: clamp(1.5rem, 3.5vw, 2rem); }
h3 { font-size: clamp(1.25rem, 3vw, 1.75rem); }
h4 { font-size: clamp(1.125rem, 2.5vw, 1.5rem); }
h5 { font-size: clamp(1rem, 2vw, 1.25rem); }
h6 { font-size: clamp(0.875rem, 1.5vw, 1rem); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
    line-height: 1.6;
}

/* الروابط التكيفية */
a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover,
a:focus {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* الأزرار التكيفية */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: var(--border-width) solid transparent;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 44px;
    min-width: 44px;
    position: relative;
    overflow: hidden;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}

.btn:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: var(--opacity-disabled);
    cursor: not-allowed;
    pointer-events: none;
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    min-height: 36px;
    min-width: 36px;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
    min-height: 52px;
    min-width: 52px;
}

/* أنواع الأزرار */
.btn-primary {
    background-color: var(--color-primary);
    color: var(--text-inverse);
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: var(--text-inverse);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: var(--text-inverse);
}

/* البطاقات التكيفية */
.card {
    background-color: var(--bg-primary);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-width) solid var(--border-color);
    background-color: var(--bg-secondary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: var(--border-width) solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* النماذج التكيفية */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-md);
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    min-height: 44px;
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control::placeholder {
    color: var(--text-tertiary);
    opacity: 1;
}

/* الجداول التكيفية */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: var(--border-width) solid var(--border-color);
    vertical-align: middle;
}

.table th {
    background-color: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

/* الأدوات المساعدة */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }

/* المسافات */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* الاستعلامات الإعلامية المتقدمة */

/* الشاشات الصغيرة جداً (الهواتف الصغيرة) */
@media (max-width: 320px) {
    :root {
        --container-padding: 0.75rem;
        --grid-gap: 0.75rem;
        --font-size-base: 14px;
    }

    .container {
        padding: 0 var(--container-padding);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }

    .card-body {
        padding: var(--spacing-md);
    }

    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.25rem; }
    h3 { font-size: 1.125rem; }
}

/* الشاشات الصغيرة (الهواتف) */
@media (max-width: 480px) {
    :root {
        --container-padding: 1rem;
        --grid-gap: 1rem;
    }

    .container {
        max-width: 100%;
    }

    .row {
        margin: 0 calc(var(--grid-gap) * -0.5);
    }

    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }

    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }

    .text-sm-center { text-align: center !important; }
    .text-sm-left { text-align: left !important; }
    .text-sm-right { text-align: right !important; }
}

/* الشاشات المتوسطة (الأجهزة اللوحية) */
@media (min-width: 768px) {
    :root {
        --container-padding: 1.5rem;
        --grid-gap: 1.5rem;
    }

    .container {
        max-width: 720px;
    }

    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }

    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }

    .text-md-center { text-align: center !important; }
    .text-md-left { text-align: left !important; }
    .text-md-right { text-align: right !important; }
}

/* الشاشات الكبيرة (أجهزة الكمبيوتر المحمولة) */
@media (min-width: 1024px) {
    :root {
        --container-padding: 2rem;
        --grid-gap: 2rem;
    }

    .container {
        max-width: 960px;
    }

    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }

    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }

    .text-lg-center { text-align: center !important; }
    .text-lg-left { text-align: left !important; }
    .text-lg-right { text-align: right !important; }
}

/* الشاشات الكبيرة جداً (أجهزة الكمبيوتر المكتبية) */
@media (min-width: 1280px) {
    .container {
        max-width: 1200px;
    }

    .col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
    .col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-xl-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
    .col-xl-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-xl-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-xl-9 { flex: 0 0 75%; max-width: 75%; }
    .col-xl-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-xl-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-xl-12 { flex: 0 0 100%; max-width: 100%; }

    .d-xl-none { display: none !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }
}

/* الشاشات العملاقة */
@media (min-width: 1536px) {
    .container {
        max-width: 1400px;
    }
}

/* تحسينات خاصة بالأجهزة المحمولة */
@media (max-width: 768px) {
    /* تحسين الأزرار للمس */
    .btn {
        min-height: 48px;
        min-width: 48px;
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .btn-sm {
        min-height: 40px;
        min-width: 40px;
    }

    /* تحسين النماذج للمس */
    .form-control {
        min-height: 48px;
        font-size: 16px; /* منع التكبير في iOS */
    }

    /* تحسين الجداول للمس */
    .table th,
    .table td {
        padding: var(--spacing-lg);
        min-height: 48px;
    }

    /* تحسين البطاقات للمس */
    .card {
        margin-bottom: var(--spacing-lg);
    }

    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-lg);
    }

    /* تحسين التنقل للمس */
    .nav-link {
        min-height: 48px;
        display: flex;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

/* تحسينات خاصة بالأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-xl);
    }

    /* تحسين الشبكة للأجهزة اللوحية */
    .tablet-col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .tablet-col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .tablet-col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
}

/* تحسينات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسينات للطباعة */
@media print {
    :root {
        --bg-primary: #ffffff;
        --bg-secondary: #ffffff;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .no-print {
        display: none !important;
    }

    .btn,
    .form-control {
        border: 1px solid #000000 !important;
    }

    .card {
        border: 1px solid #000000 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
}

/* تحسينات لوضع المناظر الطبيعية */
@media (orientation: landscape) and (max-height: 500px) {
    .landscape-hide {
        display: none !important;
    }

    .navbar {
        min-height: 50px;
    }

    .modal-dialog {
        margin: 10px auto;
    }
}

/* تحسينات لوضع البورتريه */
@media (orientation: portrait) {
    .portrait-only {
        display: block !important;
    }

    .landscape-only {
        display: none !important;
    }
}

/* تحسينات للشاشات الضيقة جداً */
@media (max-width: 360px) {
    .ultra-narrow-hide {
        display: none !important;
    }

    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }
}

/* تحسينات للشاشات العريضة جداً */
@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
    }

    .ultra-wide-show {
        display: block !important;
    }
}
