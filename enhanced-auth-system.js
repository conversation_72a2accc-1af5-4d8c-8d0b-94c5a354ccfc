/**
 * نظام المصادقة والأمان المحسن
 * Enhanced Authentication & Security System
 * 
 * المميزات:
 * - تشفير كلمات المرور
 * - إدارة الجلسات المتقدمة
 * - مستويات الأمان المختلفة
 * - تسجيل محاولات الدخول
 * - نظام الأذونات المتقدم
 */

// إعدادات الأمان
const SECURITY_CONFIG = {
    SESSION_TIMEOUT: 30 * 60 * 1000, // 30 دقيقة
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 15 * 60 * 1000, // 15 دقيقة
    PASSWORD_MIN_LENGTH: 8,
    REQUIRE_STRONG_PASSWORD: true,
    SESSION_STORAGE_KEY: 'dental_lab_session',
    FAILED_ATTEMPTS_KEY: 'failed_login_attempts'
};

// أدوار المستخدمين والأذونات
const USER_ROLES = {
    SUPER_ADMIN: {
        name: 'مدي<PERSON> عام',
        level: 100,
        permissions: ['*'] // جميع الأذونات
    },
    ADMIN: {
        name: 'مدير',
        level: 80,
        permissions: [
            'manage_users', 'manage_doctors', 'manage_employees',
            'manage_prostheses', 'view_reports', 'manage_finances',
            'manage_settings', 'export_data'
        ]
    },
    MANAGER: {
        name: 'مدير فرع',
        level: 60,
        permissions: [
            'manage_doctors', 'manage_prostheses', 'view_reports',
            'manage_employees', 'view_finances'
        ]
    },
    EMPLOYEE: {
        name: 'موظف',
        level: 40,
        permissions: [
            'add_prostheses', 'edit_own_prostheses', 'view_own_reports'
        ]
    },
    TECHNICIAN: {
        name: 'فني',
        level: 30,
        permissions: [
            'view_prostheses', 'update_prosthesis_status'
        ]
    },
    VIEWER: {
        name: 'مشاهد',
        level: 10,
        permissions: ['view_prostheses', 'view_basic_reports']
    }
};

/**
 * فئة إدارة المصادقة والأمان
 */
class EnhancedAuthSystem {
    constructor() {
        this.currentUser = null;
        this.sessionTimer = null;
        this.initializeSystem();
    }

    /**
     * تهيئة النظام
     */
    initializeSystem() {
        this.loadSession();
        this.setupSessionTimeout();
        this.setupPasswordValidation();
        this.cleanupOldAttempts();
    }

    /**
     * تشفير كلمة المرور
     */
    async hashPassword(password) {
        // استخدام Web Crypto API للتشفير
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'dental_lab_salt_2025');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    validatePasswordStrength(password) {
        const requirements = {
            minLength: password.length >= SECURITY_CONFIG.PASSWORD_MIN_LENGTH,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumbers: /\d/.test(password),
            hasSpecialChars: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };

        const score = Object.values(requirements).filter(Boolean).length;
        
        return {
            isValid: requirements.minLength && (!SECURITY_CONFIG.REQUIRE_STRONG_PASSWORD || score >= 4),
            score: score,
            requirements: requirements,
            strength: this.getPasswordStrengthText(score)
        };
    }

    /**
     * الحصول على نص قوة كلمة المرور
     */
    getPasswordStrengthText(score) {
        if (score <= 2) return { text: 'ضعيفة', color: '#dc3545', level: 'weak' };
        if (score <= 3) return { text: 'متوسطة', color: '#ffc107', level: 'medium' };
        if (score <= 4) return { text: 'قوية', color: '#28a745', level: 'strong' };
        return { text: 'قوية جداً', color: '#0d47a1', level: 'very-strong' };
    }

    /**
     * تسجيل الدخول
     */
    async login(username, password, rememberMe = false) {
        try {
            // التحقق من محاولات الدخول الفاشلة
            if (this.isAccountLocked(username)) {
                throw new Error('الحساب مقفل مؤقتاً بسبب محاولات دخول فاشلة متعددة');
            }

            // البحث عن المستخدم
            const users = JSON.parse(localStorage.getItem('dental_lab_data'))?.users || [];
            const user = users.find(u => u.username === username);

            if (!user) {
                this.recordFailedAttempt(username);
                throw new Error('اسم المستخدم غير صحيح');
            }

            // التحقق من كلمة المرور
            const hashedPassword = await this.hashPassword(password);
            const storedPassword = user.hashedPassword || await this.hashPassword(user.password);

            if (hashedPassword !== storedPassword) {
                this.recordFailedAttempt(username);
                throw new Error('كلمة المرور غير صحيحة');
            }

            // التحقق من حالة الحساب
            if (!user.isActive) {
                throw new Error('الحساب غير مفعل');
            }

            // تسجيل دخول ناجح
            this.clearFailedAttempts(username);
            await this.createSession(user, rememberMe);
            this.logSecurityEvent('login_success', username);

            return {
                success: true,
                user: this.sanitizeUserData(user),
                message: 'تم تسجيل الدخول بنجاح'
            };

        } catch (error) {
            this.logSecurityEvent('login_failed', username, error.message);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * إنشاء جلسة جديدة
     */
    async createSession(user, rememberMe) {
        const sessionData = {
            userId: user.id,
            username: user.username,
            role: user.role,
            permissions: this.getUserPermissions(user.role),
            loginTime: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            rememberMe: rememberMe,
            sessionId: this.generateSessionId()
        };

        // حفظ الجلسة
        localStorage.setItem(SECURITY_CONFIG.SESSION_STORAGE_KEY, JSON.stringify(sessionData));
        
        // تحديث آخر تسجيل دخول للمستخدم
        await this.updateLastLogin(user.id);
        
        this.currentUser = sessionData;
        this.setupSessionTimeout();
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    async updateLastLogin(userId) {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const userIndex = data.users?.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            data.users[userIndex].lastLogin = new Date().toISOString();
            data.users[userIndex].loginCount = (data.users[userIndex].loginCount || 0) + 1;
            localStorage.setItem('dental_lab_data', JSON.stringify(data));
        }
    }

    /**
     * تحميل الجلسة المحفوظة
     */
    loadSession() {
        try {
            const sessionData = localStorage.getItem(SECURITY_CONFIG.SESSION_STORAGE_KEY);
            if (!sessionData) return false;

            const session = JSON.parse(sessionData);
            
            // التحقق من انتهاء صلاحية الجلسة
            const lastActivity = new Date(session.lastActivity);
            const now = new Date();
            const timeDiff = now - lastActivity;

            if (timeDiff > SECURITY_CONFIG.SESSION_TIMEOUT && !session.rememberMe) {
                this.logout();
                return false;
            }

            // تحديث آخر نشاط
            session.lastActivity = now.toISOString();
            localStorage.setItem(SECURITY_CONFIG.SESSION_STORAGE_KEY, JSON.stringify(session));
            
            this.currentUser = session;
            return true;

        } catch (error) {
            console.error('خطأ في تحميل الجلسة:', error);
            this.logout();
            return false;
        }
    }

    /**
     * تسجيل الخروج
     */
    logout() {
        if (this.currentUser) {
            this.logSecurityEvent('logout', this.currentUser.username);
        }

        localStorage.removeItem(SECURITY_CONFIG.SESSION_STORAGE_KEY);
        this.currentUser = null;
        
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
            this.sessionTimer = null;
        }

        // إعادة توجيه لصفحة تسجيل الدخول
        this.redirectToLogin();
    }

    /**
     * التحقق من الأذونات
     */
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        const userPermissions = this.currentUser.permissions || [];
        return userPermissions.includes('*') || userPermissions.includes(permission);
    }

    /**
     * الحصول على أذونات المستخدم
     */
    getUserPermissions(role) {
        return USER_ROLES[role]?.permissions || [];
    }

    /**
     * تسجيل محاولة فاشلة
     */
    recordFailedAttempt(username) {
        const attempts = JSON.parse(localStorage.getItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY)) || {};
        const now = new Date().toISOString();
        
        if (!attempts[username]) {
            attempts[username] = [];
        }
        
        attempts[username].push(now);
        localStorage.setItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY, JSON.stringify(attempts));
    }

    /**
     * التحقق من قفل الحساب
     */
    isAccountLocked(username) {
        const attempts = JSON.parse(localStorage.getItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY)) || {};
        const userAttempts = attempts[username] || [];
        
        if (userAttempts.length < SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
            return false;
        }
        
        const lastAttempt = new Date(userAttempts[userAttempts.length - 1]);
        const now = new Date();
        
        return (now - lastAttempt) < SECURITY_CONFIG.LOCKOUT_DURATION;
    }

    /**
     * مسح محاولات فاشلة
     */
    clearFailedAttempts(username) {
        const attempts = JSON.parse(localStorage.getItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY)) || {};
        delete attempts[username];
        localStorage.setItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY, JSON.stringify(attempts));
    }

    /**
     * تنظيف المحاولات القديمة
     */
    cleanupOldAttempts() {
        const attempts = JSON.parse(localStorage.getItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY)) || {};
        const now = new Date();
        
        Object.keys(attempts).forEach(username => {
            attempts[username] = attempts[username].filter(attemptTime => {
                const attemptDate = new Date(attemptTime);
                return (now - attemptDate) < SECURITY_CONFIG.LOCKOUT_DURATION;
            });
            
            if (attempts[username].length === 0) {
                delete attempts[username];
            }
        });
        
        localStorage.setItem(SECURITY_CONFIG.FAILED_ATTEMPTS_KEY, JSON.stringify(attempts));
    }

    /**
     * توليد معرف جلسة
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * تنظيف بيانات المستخدم
     */
    sanitizeUserData(user) {
        const { password, hashedPassword, ...safeUser } = user;
        return safeUser;
    }

    /**
     * إعداد مهلة الجلسة
     */
    setupSessionTimeout() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }

        if (this.currentUser && !this.currentUser.rememberMe) {
            this.sessionTimer = setTimeout(() => {
                this.logout();
                alert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
            }, SECURITY_CONFIG.SESSION_TIMEOUT);
        }
    }

    /**
     * تسجيل أحداث الأمان
     */
    logSecurityEvent(event, username, details = '') {
        const securityLog = JSON.parse(localStorage.getItem('security_log')) || [];
        
        securityLog.push({
            timestamp: new Date().toISOString(),
            event: event,
            username: username,
            details: details,
            ip: 'localhost', // في بيئة حقيقية يمكن الحصول على IP
            userAgent: navigator.userAgent
        });

        // الاحتفاظ بآخر 1000 حدث فقط
        if (securityLog.length > 1000) {
            securityLog.splice(0, securityLog.length - 1000);
        }

        localStorage.setItem('security_log', JSON.stringify(securityLog));
    }

    /**
     * إعداد التحقق من كلمة المرور
     */
    setupPasswordValidation() {
        // سيتم استخدامها في واجهة المستخدم
        this.passwordValidationRules = {
            minLength: SECURITY_CONFIG.PASSWORD_MIN_LENGTH,
            requireUpperCase: SECURITY_CONFIG.REQUIRE_STRONG_PASSWORD,
            requireLowerCase: SECURITY_CONFIG.REQUIRE_STRONG_PASSWORD,
            requireNumbers: SECURITY_CONFIG.REQUIRE_STRONG_PASSWORD,
            requireSpecialChars: SECURITY_CONFIG.REQUIRE_STRONG_PASSWORD
        };
    }

    /**
     * إعادة توجيه لصفحة تسجيل الدخول
     */
    redirectToLogin() {
        // إخفاء المحتوى الرئيسي وإظهار نموذج تسجيل الدخول
        const contentArea = document.getElementById('content-area');
        const loginContainer = document.getElementById('login-container');
        const mainNav = document.getElementById('main-nav');
        
        if (contentArea) contentArea.innerHTML = '';
        if (loginContainer) loginContainer.style.display = 'block';
        if (mainNav) mainNav.style.display = 'none';
    }

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * التحقق من تسجيل الدخول
     */
    isLoggedIn() {
        return this.currentUser !== null;
    }

    /**
     * تحديث نشاط الجلسة
     */
    updateActivity() {
        if (this.currentUser) {
            this.currentUser.lastActivity = new Date().toISOString();
            localStorage.setItem(SECURITY_CONFIG.SESSION_STORAGE_KEY, JSON.stringify(this.currentUser));
            this.setupSessionTimeout();
        }
    }
}

// إنشاء مثيل عام من نظام المصادقة
const authSystem = new EnhancedAuthSystem();

    /**
     * تغيير كلمة المرور
     */
    async changePassword(currentPassword, newPassword) {
        if (!this.currentUser) {
            throw new Error('يجب تسجيل الدخول أولاً');
        }

        // التحقق من كلمة المرور الحالية
        const users = JSON.parse(localStorage.getItem('dental_lab_data'))?.users || [];
        const user = users.find(u => u.id === this.currentUser.userId);

        if (!user) {
            throw new Error('المستخدم غير موجود');
        }

        const currentHashedPassword = await this.hashPassword(currentPassword);
        const storedPassword = user.hashedPassword || await this.hashPassword(user.password);

        if (currentHashedPassword !== storedPassword) {
            throw new Error('كلمة المرور الحالية غير صحيحة');
        }

        // التحقق من قوة كلمة المرور الجديدة
        const validation = this.validatePasswordStrength(newPassword);
        if (!validation.isValid) {
            throw new Error('كلمة المرور الجديدة لا تلبي متطلبات الأمان');
        }

        // تحديث كلمة المرور
        const newHashedPassword = await this.hashPassword(newPassword);
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const userIndex = data.users?.findIndex(u => u.id === this.currentUser.userId);

        if (userIndex !== -1) {
            data.users[userIndex].hashedPassword = newHashedPassword;
            delete data.users[userIndex].password; // إزالة كلمة المرور القديمة غير المشفرة
            data.users[userIndex].passwordChangedAt = new Date().toISOString();
            localStorage.setItem('dental_lab_data', JSON.stringify(data));
        }

        this.logSecurityEvent('password_changed', this.currentUser.username);
        return true;
    }

    /**
     * إنشاء مستخدم جديد
     */
    async createUser(userData) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لإنشاء مستخدمين جدد');
        }

        // التحقق من البيانات المطلوبة
        if (!userData.username || !userData.password || !userData.role) {
            throw new Error('البيانات المطلوبة غير مكتملة');
        }

        // التحقق من عدم وجود المستخدم مسبقاً
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const existingUser = data.users?.find(u => u.username === userData.username);

        if (existingUser) {
            throw new Error('اسم المستخدم موجود مسبقاً');
        }

        // التحقق من صحة الدور
        if (!USER_ROLES[userData.role]) {
            throw new Error('الدور المحدد غير صحيح');
        }

        // التحقق من قوة كلمة المرور
        const validation = this.validatePasswordStrength(userData.password);
        if (!validation.isValid) {
            throw new Error('كلمة المرور لا تلبي متطلبات الأمان');
        }

        // إنشاء المستخدم الجديد
        const hashedPassword = await this.hashPassword(userData.password);
        const newUser = {
            id: (data.nextId?.users || 1),
            username: userData.username,
            hashedPassword: hashedPassword,
            role: userData.role,
            fullName: userData.fullName || '',
            email: userData.email || '',
            phone: userData.phone || '',
            isActive: userData.isActive !== false,
            createdBy: this.currentUser.userId,
            createdAt: new Date().toISOString(),
            lastLogin: null,
            loginCount: 0
        };

        // حفظ المستخدم
        if (!data.users) data.users = [];
        if (!data.nextId) data.nextId = {};

        data.users.push(newUser);
        data.nextId.users = (data.nextId.users || 1) + 1;

        localStorage.setItem('dental_lab_data', JSON.stringify(data));

        this.logSecurityEvent('user_created', this.currentUser.username, `Created user: ${userData.username}`);

        return this.sanitizeUserData(newUser);
    }

    /**
     * تحديث بيانات المستخدم
     */
    async updateUser(userId, updateData) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لتحديث المستخدمين');
        }

        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const userIndex = data.users?.findIndex(u => u.id === userId);

        if (userIndex === -1) {
            throw new Error('المستخدم غير موجود');
        }

        const user = data.users[userIndex];

        // التحقق من الصلاحيات (لا يمكن تعديل مستخدم بمستوى أعلى)
        const currentUserRole = USER_ROLES[this.currentUser.role];
        const targetUserRole = USER_ROLES[user.role];

        if (targetUserRole.level >= currentUserRole.level && user.id !== this.currentUser.userId) {
            throw new Error('ليس لديك صلاحية لتحديث هذا المستخدم');
        }

        // تحديث البيانات المسموحة
        const allowedFields = ['fullName', 'email', 'phone', 'isActive'];
        if (this.hasPermission('manage_users') && updateData.role && USER_ROLES[updateData.role]) {
            allowedFields.push('role');
        }

        allowedFields.forEach(field => {
            if (updateData[field] !== undefined) {
                user[field] = updateData[field];
            }
        });

        user.updatedAt = new Date().toISOString();
        user.updatedBy = this.currentUser.userId;

        localStorage.setItem('dental_lab_data', JSON.stringify(data));

        this.logSecurityEvent('user_updated', this.currentUser.username, `Updated user: ${user.username}`);

        return this.sanitizeUserData(user);
    }

    /**
     * حذف مستخدم
     */
    async deleteUser(userId) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لحذف المستخدمين');
        }

        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const userIndex = data.users?.findIndex(u => u.id === userId);

        if (userIndex === -1) {
            throw new Error('المستخدم غير موجود');
        }

        const user = data.users[userIndex];

        // منع حذف المستخدم الحالي
        if (user.id === this.currentUser.userId) {
            throw new Error('لا يمكن حذف حسابك الخاص');
        }

        // التحقق من الصلاحيات
        const currentUserRole = USER_ROLES[this.currentUser.role];
        const targetUserRole = USER_ROLES[user.role];

        if (targetUserRole.level >= currentUserRole.level) {
            throw new Error('ليس لديك صلاحية لحذف هذا المستخدم');
        }

        // حذف المستخدم
        data.users.splice(userIndex, 1);
        localStorage.setItem('dental_lab_data', JSON.stringify(data));

        this.logSecurityEvent('user_deleted', this.currentUser.username, `Deleted user: ${user.username}`);

        return true;
    }

    /**
     * الحصول على جميع المستخدمين
     */
    getAllUsers() {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لعرض المستخدمين');
        }

        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const users = data.users || [];

        return users.map(user => this.sanitizeUserData(user));
    }

    /**
     * الحصول على سجل الأمان
     */
    getSecurityLog(limit = 100) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لعرض سجل الأمان');
        }

        const securityLog = JSON.parse(localStorage.getItem('security_log')) || [];
        return securityLog.slice(-limit).reverse(); // آخر الأحداث أولاً
    }

    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    cleanupExpiredSessions() {
        // في تطبيق حقيقي، هذا سيكون في قاعدة البيانات
        // هنا نقوم بتنظيف البيانات المحلية فقط
        this.cleanupOldAttempts();

        // تنظيف سجل الأمان القديم (الاحتفاظ بآخر 30 يوم)
        const securityLog = JSON.parse(localStorage.getItem('security_log')) || [];
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const filteredLog = securityLog.filter(entry => {
            const entryDate = new Date(entry.timestamp);
            return entryDate > thirtyDaysAgo;
        });

        localStorage.setItem('security_log', JSON.stringify(filteredLog));
    }
}

// إنشاء مثيل عام من نظام المصادقة
const authSystem = new EnhancedAuthSystem();

// تنظيف دوري للجلسات المنتهية الصلاحية
setInterval(() => {
    authSystem.cleanupExpiredSessions();
}, 60 * 60 * 1000); // كل ساعة

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedAuthSystem, authSystem, USER_ROLES, SECURITY_CONFIG };
}
