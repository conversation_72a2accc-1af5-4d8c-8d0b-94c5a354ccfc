/**
 * نظام إدارة الأطباء المحسن
 * Enhanced Doctors Management System
 * 
 * المميزات:
 * - ملفات شخصية مفصلة للأطباء
 * - تاريخ العمل والإحصائيات
 * - إعدادات الأسعار المتقدمة
 * - نظام التقييمات والمراجعات
 * - إدارة العقود والاتفاقيات
 */

class EnhancedDoctorsManagement {
    constructor() {
        this.doctors = [];
        this.doctorPrices = {};
        this.doctorContracts = {};
        this.doctorReviews = {};
        this.loadData();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        this.doctors = data.doctors || [];
        this.doctorPrices = data.doctorPrices || {};
        this.doctorContracts = data.doctorContracts || {};
        this.doctorReviews = data.doctorReviews || {};
    }

    /**
     * إنشاء HTML لإدارة الأطباء
     */
    generateDoctorsManagementHTML() {
        return `
            <div class="doctors-management-container">
                <!-- شريط الأدوات العلوي -->
                <div class="toolbar mb-4">
                    <div class="row align-items-center">
                        <div class="col-12 col-md-6">
                            <h2 class="page-title mb-0">
                                <i class="fas fa-user-md text-primary me-2"></i>
                                إدارة الأطباء
                            </h2>
                        </div>
                        <div class="col-12 col-md-6 text-md-end mt-3 mt-md-0">
                            <button class="btn btn-primary" onclick="doctorsManager.showAddDoctorModal()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة طبيب جديد
                            </button>
                            <button class="btn btn-outline-secondary ms-2" onclick="doctorsManager.exportDoctorsData()">
                                <i class="fas fa-download me-1"></i>
                                تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-12 col-sm-6 col-lg-3 mb-3">
                        <div class="stats-card stats-primary">
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-number">${this.doctors.length}</div>
                                <div class="stats-label">إجمالي الأطباء</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 col-lg-3 mb-3">
                        <div class="stats-card stats-success">
                            <div class="stats-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-number">${this.doctors.filter(d => d.status === 'active').length}</div>
                                <div class="stats-label">الأطباء النشطون</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 col-lg-3 mb-3">
                        <div class="stats-card stats-info">
                            <div class="stats-icon">
                                <i class="fas fa-tooth"></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-number">${this.getTotalProsthesesThisMonth()}</div>
                                <div class="stats-label">تركيبات هذا الشهر</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 col-lg-3 mb-3">
                        <div class="stats-card stats-warning">
                            <div class="stats-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-number">${this.calculateAverageRating().toFixed(1)}</div>
                                <div class="stats-label">متوسط التقييم</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 col-md-3 mb-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="doctors-search"
                                           placeholder="ابحث بالاسم أو الهاتف..."
                                           onkeyup="doctorsManager.filterDoctors()">
                                </div>
                            </div>

                            <div class="col-12 col-md-2 mb-3">
                                <label class="form-label">التخصص</label>
                                <select class="form-control" id="specialty-filter" onchange="doctorsManager.filterDoctors()">
                                    <option value="">جميع التخصصات</option>
                                    <option value="general">طب أسنان عام</option>
                                    <option value="orthodontics">تقويم الأسنان</option>
                                    <option value="surgery">جراحة الفم</option>
                                    <option value="prosthodontics">تركيبات الأسنان</option>
                                    <option value="endodontics">علاج الجذور</option>
                                    <option value="periodontics">أمراض اللثة</option>
                                </select>
                            </div>

                            <div class="col-12 col-md-2 mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="status-filter" onchange="doctorsManager.filterDoctors()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">معلق</option>
                                </select>
                            </div>

                            <div class="col-12 col-md-2 mb-3">
                                <label class="form-label">التقييم</label>
                                <select class="form-control" id="rating-filter" onchange="doctorsManager.filterDoctors()">
                                    <option value="">جميع التقييمات</option>
                                    <option value="4.5">4.5+ نجوم</option>
                                    <option value="4">4+ نجوم</option>
                                    <option value="3.5">3.5+ نجوم</option>
                                    <option value="3">3+ نجوم</option>
                                </select>
                            </div>

                            <div class="col-12 col-md-3 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-secondary flex-fill" onclick="doctorsManager.resetFilters()">
                                        <i class="fas fa-undo me-1"></i>
                                        إعادة تعيين
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="doctorsManager.toggleViewMode()">
                                        <i class="fas fa-th-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الأطباء -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">قائمة الأطباء (${this.doctors.length})</h5>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="doctorsManager.refreshDoctors()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="doctors-grid" class="doctors-grid">
                            ${this.generateDoctorsGrid()}
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل طبيب -->
            <div class="modal fade" id="doctor-modal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="doctor-modal-title">إضافة طبيب جديد</h5>
                            <button type="button" class="btn-close" onclick="doctorsManager.closeDoctorModal()"></button>
                        </div>
                        <div class="modal-body">
                            ${this.generateDoctorForm()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء شبكة الأطباء
     */
    generateDoctorsGrid() {
        if (this.doctors.length === 0) {
            return `
                <div class="empty-state p-5 text-center">
                    <i class="fas fa-user-md text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                    <h4 class="text-muted">لا يوجد أطباء</h4>
                    <p class="text-muted">ابدأ بإضافة أول طبيب للنظام</p>
                    <button class="btn btn-primary" onclick="doctorsManager.showAddDoctorModal()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة طبيب جديد
                    </button>
                </div>
            `;
        }

        return this.doctors.map(doctor => this.generateDoctorCard(doctor)).join('');
    }

    /**
     * الحصول على إجمالي التركيبات هذا الشهر
     */
    getTotalProsthesesThisMonth() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const prostheses = data.prostheses || [];
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        return prostheses.filter(p => new Date(p.created_at) >= thisMonth).length;
    }

    /**
     * حفظ البيانات
     */
    saveData() {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        data.doctors = this.doctors;
        data.doctorPrices = this.doctorPrices;
        data.doctorContracts = this.doctorContracts;
        data.doctorReviews = this.doctorReviews;
        localStorage.setItem('dental_lab_data', JSON.stringify(data));
    }

    /**
     * إنشاء HTML لوحدة إدارة الأطباء
     */
    generateDoctorsManagementHTML() {
        return `
            <div class="enhanced-doctors-management">
                <!-- رأس الوحدة -->
                <div class="module-header">
                    <div class="header-content">
                        <h1 class="module-title">
                            <i class="fas fa-user-md"></i>
                            إدارة الأطباء المتقدمة
                        </h1>
                        <p class="module-subtitle">
                            إدارة شاملة لملفات الأطباء وإعداداتهم
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="doctorsManager.showAddDoctorModal()">
                            <i class="fas fa-plus"></i>
                            إضافة طبيب جديد
                        </button>
                        <button class="btn btn-secondary" onclick="doctorsManager.exportDoctorsData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="doctors-stats">
                    ${this.generateDoctorsStats()}
                </div>

                <!-- شريط البحث والفلاتر -->
                <div class="search-filters-section">
                    <div class="search-bar">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" id="doctors-search" placeholder="البحث عن طبيب..." 
                                   onkeyup="doctorsManager.filterDoctors()">
                        </div>
                    </div>
                    <div class="filters-group">
                        <select id="specialty-filter" onchange="doctorsManager.filterDoctors()">
                            <option value="">جميع التخصصات</option>
                            <option value="general">عام</option>
                            <option value="orthodontics">تقويم</option>
                            <option value="surgery">جراحة</option>
                            <option value="pediatric">أطفال</option>
                            <option value="prosthodontics">تركيبات</option>
                        </select>
                        <select id="status-filter" onchange="doctorsManager.filterDoctors()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                        <select id="rating-filter" onchange="doctorsManager.filterDoctors()">
                            <option value="">جميع التقييمات</option>
                            <option value="5">5 نجوم</option>
                            <option value="4">4 نجوم فأكثر</option>
                            <option value="3">3 نجوم فأكثر</option>
                        </select>
                    </div>
                </div>

                <!-- قائمة الأطباء -->
                <div class="doctors-grid" id="doctors-grid">
                    ${this.generateDoctorsGrid()}
                </div>

                <!-- نافذة إضافة/تعديل طبيب -->
                <div id="doctor-modal" class="modal" style="display: none;">
                    <div class="modal-content large">
                        <div class="modal-header">
                            <h3 id="doctor-modal-title">إضافة طبيب جديد</h3>
                            <span class="close" onclick="doctorsManager.closeDoctorModal()">&times;</span>
                        </div>
                        <div class="modal-body">
                            ${this.generateDoctorForm()}
                        </div>
                    </div>
                </div>

                <!-- نافذة تفاصيل الطبيب -->
                <div id="doctor-details-modal" class="modal" style="display: none;">
                    <div class="modal-content extra-large">
                        <div class="modal-header">
                            <h3>تفاصيل الطبيب</h3>
                            <span class="close" onclick="doctorsManager.closeDoctorDetailsModal()">&times;</span>
                        </div>
                        <div class="modal-body" id="doctor-details-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>
                </div>

                <!-- نافذة إعدادات الأسعار -->
                <div id="doctor-prices-modal" class="modal" style="display: none;">
                    <div class="modal-content large">
                        <div class="modal-header">
                            <h3>إعدادات الأسعار</h3>
                            <span class="close" onclick="doctorsManager.closePricesModal()">&times;</span>
                        </div>
                        <div class="modal-body" id="doctor-prices-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء إحصائيات الأطباء
     */
    generateDoctorsStats() {
        const totalDoctors = this.doctors.length;
        const activeDoctors = this.doctors.filter(d => d.status === 'active').length;
        const avgRating = this.calculateAverageRating();
        const topPerformer = this.getTopPerformer();

        return `
            <div class="stats-cards">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${totalDoctors}</div>
                        <div class="stat-label">إجمالي الأطباء</div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${activeDoctors}</div>
                        <div class="stat-label">الأطباء النشطون</div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${avgRating.toFixed(1)}</div>
                        <div class="stat-label">متوسط التقييم</div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">${topPerformer ? topPerformer.name : 'لا يوجد'}</div>
                        <div class="stat-label">أفضل أداء</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء شبكة الأطباء
     */
    generateDoctorsGrid() {
        if (this.doctors.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-user-md"></i>
                    <h3>لا يوجد أطباء مسجلون</h3>
                    <p>ابدأ بإضافة أول طبيب للنظام</p>
                    <button class="btn btn-primary" onclick="doctorsManager.showAddDoctorModal()">
                        <i class="fas fa-plus"></i>
                        إضافة طبيب جديد
                    </button>
                </div>
            `;
        }

        return this.doctors.map(doctor => this.generateDoctorCard(doctor)).join('');
    }

    /**
     * إنشاء بطاقة طبيب
     */
    generateDoctorCard(doctor) {
        const rating = this.getDoctorRating(doctor.id);
        const prosthesesCount = this.getDoctorProsthesesCount(doctor.id);
        const revenue = this.getDoctorRevenue(doctor.id);
        const statusClass = doctor.status || 'active';
        const statusText = this.getStatusText(doctor.status);

        return `
            <div class="doctor-card ${statusClass}" data-doctor-id="${doctor.id}">
                <div class="doctor-header">
                    <div class="doctor-avatar">
                        ${doctor.avatar ? 
                            `<img src="${doctor.avatar}" alt="${doctor.name}">` : 
                            `<i class="fas fa-user-md"></i>`
                        }
                        <div class="status-indicator ${statusClass}"></div>
                    </div>
                    <div class="doctor-basic-info">
                        <h3 class="doctor-name">${doctor.name}</h3>
                        <p class="doctor-specialty">${this.getSpecialtyText(doctor.specialty)}</p>
                        <div class="doctor-rating">
                            ${this.generateStarRating(rating)}
                            <span class="rating-text">(${rating.toFixed(1)})</span>
                        </div>
                    </div>
                    <div class="doctor-status">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                </div>

                <div class="doctor-stats">
                    <div class="stat-item">
                        <i class="fas fa-tooth"></i>
                        <span class="stat-value">${prosthesesCount}</span>
                        <span class="stat-label">تركيبة</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-money-bill-wave"></i>
                        <span class="stat-value">${revenue.toLocaleString()}</span>
                        <span class="stat-label">ج.م</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-calendar"></i>
                        <span class="stat-value">${this.getJoinedDuration(doctor.created_at)}</span>
                        <span class="stat-label">منذ الانضمام</span>
                    </div>
                </div>

                <div class="doctor-contact">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>${doctor.phone || 'غير محدد'}</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${doctor.clinic_address || 'غير محدد'}</span>
                    </div>
                </div>

                <div class="doctor-actions">
                    <button class="btn btn-sm btn-primary" onclick="doctorsManager.viewDoctorDetails(${doctor.id})">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="doctorsManager.editDoctor(${doctor.id})">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-info" onclick="doctorsManager.managePrices(${doctor.id})">
                        <i class="fas fa-dollar-sign"></i>
                        الأسعار
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline dropdown-toggle" onclick="doctorsManager.toggleDropdown(${doctor.id})">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="dropdown-menu" id="dropdown-${doctor.id}">
                            <a href="#" onclick="doctorsManager.generateDoctorReport(${doctor.id})">
                                <i class="fas fa-chart-bar"></i>
                                تقرير الأداء
                            </a>
                            <a href="#" onclick="doctorsManager.manageDoctorContract(${doctor.id})">
                                <i class="fas fa-file-contract"></i>
                                إدارة العقد
                            </a>
                            <a href="#" onclick="doctorsManager.viewDoctorHistory(${doctor.id})">
                                <i class="fas fa-history"></i>
                                تاريخ العمل
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" onclick="doctorsManager.toggleDoctorStatus(${doctor.id})" 
                               class="${doctor.status === 'active' ? 'text-warning' : 'text-success'}">
                                <i class="fas fa-${doctor.status === 'active' ? 'pause' : 'play'}"></i>
                                ${doctor.status === 'active' ? 'إيقاف مؤقت' : 'تفعيل'}
                            </a>
                            <a href="#" onclick="doctorsManager.deleteDoctor(${doctor.id})" class="text-danger">
                                <i class="fas fa-trash"></i>
                                حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء نموذج الطبيب
     */
    generateDoctorForm() {
        return `
            <form id="doctor-form" class="doctor-form">
                <div class="form-tabs">
                    <div class="tab-nav">
                        <button type="button" class="tab-btn active" onclick="doctorsManager.switchTab('basic')">
                            المعلومات الأساسية
                        </button>
                        <button type="button" class="tab-btn" onclick="doctorsManager.switchTab('contact')">
                            معلومات الاتصال
                        </button>
                        <button type="button" class="tab-btn" onclick="doctorsManager.switchTab('professional')">
                            المعلومات المهنية
                        </button>
                        <button type="button" class="tab-btn" onclick="doctorsManager.switchTab('financial')">
                            الإعدادات المالية
                        </button>
                    </div>

                    <!-- تبويب المعلومات الأساسية -->
                    <div id="basic-tab" class="tab-content active">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-name">اسم الطبيب *</label>
                                <input type="text" id="doctor-name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="doctor-specialty">التخصص *</label>
                                <select id="doctor-specialty" name="specialty" required>
                                    <option value="">اختر التخصص</option>
                                    <option value="general">طب أسنان عام</option>
                                    <option value="orthodontics">تقويم الأسنان</option>
                                    <option value="surgery">جراحة الفم والوجه</option>
                                    <option value="pediatric">طب أسنان الأطفال</option>
                                    <option value="prosthodontics">التركيبات السنية</option>
                                    <option value="endodontics">علاج الجذور</option>
                                    <option value="periodontics">أمراض اللثة</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-license">رقم الترخيص</label>
                                <input type="text" id="doctor-license" name="license">
                            </div>
                            <div class="form-group">
                                <label for="doctor-experience">سنوات الخبرة</label>
                                <input type="number" id="doctor-experience" name="experience" min="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="doctor-bio">نبذة مختصرة</label>
                            <textarea id="doctor-bio" name="bio" rows="3" 
                                      placeholder="نبذة مختصرة عن الطبيب وخبراته..."></textarea>
                        </div>
                    </div>

                    <!-- تبويب معلومات الاتصال -->
                    <div id="contact-tab" class="tab-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-phone">رقم الهاتف *</label>
                                <input type="tel" id="doctor-phone" name="phone" required>
                            </div>
                            <div class="form-group">
                                <label for="doctor-email">البريد الإلكتروني</label>
                                <input type="email" id="doctor-email" name="email">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="doctor-address">عنوان العيادة</label>
                            <textarea id="doctor-address" name="clinic_address" rows="2" 
                                      placeholder="العنوان الكامل للعيادة..."></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-city">المدينة</label>
                                <input type="text" id="doctor-city" name="city">
                            </div>
                            <div class="form-group">
                                <label for="doctor-district">المنطقة</label>
                                <input type="text" id="doctor-district" name="district">
                            </div>
                        </div>
                    </div>

                    <!-- تبويب المعلومات المهنية -->
                    <div id="professional-tab" class="tab-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-university">الجامعة</label>
                                <input type="text" id="doctor-university" name="university">
                            </div>
                            <div class="form-group">
                                <label for="doctor-graduation-year">سنة التخرج</label>
                                <input type="number" id="doctor-graduation-year" name="graduation_year" 
                                       min="1950" max="${new Date().getFullYear()}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="doctor-certifications">الشهادات والدورات</label>
                            <textarea id="doctor-certifications" name="certifications" rows="3" 
                                      placeholder="قائمة بالشهادات والدورات التدريبية..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="doctor-languages">اللغات</label>
                            <input type="text" id="doctor-languages" name="languages" 
                                   placeholder="العربية، الإنجليزية، الفرنسية...">
                        </div>
                    </div>

                    <!-- تبويب الإعدادات المالية -->
                    <div id="financial-tab" class="tab-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-default-price">السعر الافتراضي لكل سن</label>
                                <input type="number" id="doctor-default-price" name="default_price_per_tooth" 
                                       step="0.01" min="0">
                            </div>
                            <div class="form-group">
                                <label for="doctor-commission">نسبة العمولة (%)</label>
                                <input type="number" id="doctor-commission" name="commission_rate" 
                                       step="0.01" min="0" max="100">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="doctor-payment-terms">شروط الدفع</label>
                                <select id="doctor-payment-terms" name="payment_terms">
                                    <option value="immediate">فوري</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                    <option value="on_delivery">عند التسليم</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="doctor-currency">العملة</label>
                                <select id="doctor-currency" name="currency">
                                    <option value="EGP">جنيه مصري</option>
                                    <option value="USD">دولار أمريكي</option>
                                    <option value="EUR">يورو</option>
                                    <option value="SAR">ريال سعودي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ البيانات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="doctorsManager.closeDoctorModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        `;
    }

    /**
     * حساب متوسط التقييم
     */
    calculateAverageRating() {
        if (this.doctors.length === 0) return 0;

        const totalRating = this.doctors.reduce((sum, doctor) => {
            return sum + this.getDoctorRating(doctor.id);
        }, 0);

        return totalRating / this.doctors.length;
    }

    /**
     * الحصول على أفضل طبيب أداءً
     */
    getTopPerformer() {
        if (this.doctors.length === 0) return null;

        return this.doctors.reduce((best, doctor) => {
            const doctorRevenue = this.getDoctorRevenue(doctor.id);
            const bestRevenue = best ? this.getDoctorRevenue(best.id) : 0;
            return doctorRevenue > bestRevenue ? doctor : best;
        }, null);
    }

    /**
     * الحصول على تقييم الطبيب
     */
    getDoctorRating(doctorId) {
        const reviews = this.doctorReviews[doctorId] || [];
        if (reviews.length === 0) return 4.0; // تقييم افتراضي

        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        return totalRating / reviews.length;
    }

    /**
     * الحصول على عدد تركيبات الطبيب
     */
    getDoctorProsthesesCount(doctorId) {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const prostheses = data.prostheses || [];
        return prostheses.filter(p => p.doctorId === doctorId).length;
    }

    /**
     * الحصول على إيرادات الطبيب
     */
    getDoctorRevenue(doctorId) {
        const data = JSON.parse(localStorage.getItem('dental_lab_data')) || {};
        const prostheses = data.prostheses || [];
        return prostheses
            .filter(p => p.doctorId === doctorId)
            .reduce((sum, p) => sum + (p.totalPrice || 0), 0);
    }

    /**
     * الحصول على نص الحالة
     */
    getStatusText(status) {
        const statusTexts = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'suspended': 'معلق',
            'pending': 'قيد المراجعة'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * الحصول على نص التخصص
     */
    getSpecialtyText(specialty) {
        const specialtyTexts = {
            'general': 'طب أسنان عام',
            'orthodontics': 'تقويم الأسنان',
            'surgery': 'جراحة الفم والوجه',
            'pediatric': 'طب أسنان الأطفال',
            'prosthodontics': 'التركيبات السنية',
            'endodontics': 'علاج الجذور',
            'periodontics': 'أمراض اللثة'
        };
        return specialtyTexts[specialty] || specialty || 'غير محدد';
    }

    /**
     * إنشاء تقييم بالنجوم
     */
    generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';

        // نجوم ممتلئة
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }

        // نجمة نصف ممتلئة
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }

        // نجوم فارغة
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return `<div class="star-rating">${starsHTML}</div>`;
    }

    /**
     * الحصول على مدة الانضمام
     */
    getJoinedDuration(createdAt) {
        if (!createdAt) return 'غير محدد';

        const joinDate = new Date(createdAt);
        const now = new Date();
        const diffInMonths = (now.getFullYear() - joinDate.getFullYear()) * 12 +
                            (now.getMonth() - joinDate.getMonth());

        if (diffInMonths < 1) return 'أقل من شهر';
        if (diffInMonths < 12) return `${diffInMonths} شهر`;

        const years = Math.floor(diffInMonths / 12);
        const months = diffInMonths % 12;

        if (months === 0) return `${years} سنة`;
        return `${years} سنة و ${months} شهر`;
    }

    /**
     * عرض نافذة إضافة طبيب
     */
    showAddDoctorModal() {
        document.getElementById('doctor-modal-title').textContent = 'إضافة طبيب جديد';
        document.getElementById('doctor-form').reset();
        document.getElementById('doctor-modal').style.display = 'block';
        this.currentDoctorId = null;
    }

    /**
     * إغلاق نافذة الطبيب
     */
    closeDoctorModal() {
        document.getElementById('doctor-modal').style.display = 'none';
        this.currentDoctorId = null;
    }

    /**
     * تبديل التبويبات
     */
    switchTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-tab`).classList.add('active');
        event.target.classList.add('active');
    }

    /**
     * فلترة الأطباء
     */
    filterDoctors() {
        const searchTerm = document.getElementById('doctors-search').value.toLowerCase();
        const specialtyFilter = document.getElementById('specialty-filter').value;
        const statusFilter = document.getElementById('status-filter').value;
        const ratingFilter = parseFloat(document.getElementById('rating-filter').value) || 0;

        const filteredDoctors = this.doctors.filter(doctor => {
            const matchesSearch = doctor.name.toLowerCase().includes(searchTerm) ||
                                (doctor.phone && doctor.phone.includes(searchTerm)) ||
                                (doctor.clinic_address && doctor.clinic_address.toLowerCase().includes(searchTerm));

            const matchesSpecialty = !specialtyFilter || doctor.specialty === specialtyFilter;
            const matchesStatus = !statusFilter || doctor.status === statusFilter;
            const matchesRating = !ratingFilter || this.getDoctorRating(doctor.id) >= ratingFilter;

            return matchesSearch && matchesSpecialty && matchesStatus && matchesRating;
        });

        this.displayFilteredDoctors(filteredDoctors);
    }

    /**
     * عرض الأطباء المفلترين
     */
    displayFilteredDoctors(doctors) {
        const grid = document.getElementById('doctors-grid');

        if (doctors.length === 0) {
            grid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد نتائج</h3>
                    <p>لم يتم العثور على أطباء تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = doctors.map(doctor => this.generateDoctorCard(doctor)).join('');
    }

    /**
     * تصدير بيانات الأطباء
     */
    exportDoctorsData() {
        const exportData = {
            doctors: this.doctors,
            doctorPrices: this.doctorPrices,
            doctorContracts: this.doctorContracts,
            doctorReviews: this.doctorReviews,
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `doctors-data-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }
}

// إنشاء مثيل عام من إدارة الأطباء
const doctorsManager = new EnhancedDoctorsManagement();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedDoctorsManagement, doctorsManager };
}
