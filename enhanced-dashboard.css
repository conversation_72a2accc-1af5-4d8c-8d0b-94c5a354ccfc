/**
 * أنماط لوحة التحكم المحسنة
 * Enhanced Dashboard Styles
 */

/* لوحة التحكم الرئيسية */
.enhanced-dashboard {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    direction: rtl;
}

/* رأس لوحة التحكم */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.welcome-section h1.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dashboard-title i {
    color: #667eea;
}

.dashboard-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.dashboard-actions {
    display: flex;
    gap: 1rem;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* بطاقة الإحصائية */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card.primary { --card-color: #667eea; }
.stat-card.success { --card-color: #48bb78; }
.stat-card.info { --card-color: #4299e1; }
.stat-card.warning { --card-color: #ed8936; }
.stat-card.danger { --card-color: #f56565; }
.stat-card.secondary { --card-color: #a0aec0; }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--card-color);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 1rem;
    color: #718096;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive { color: #48bb78; }
.stat-change.negative { color: #f56565; }
.stat-change.neutral { color: #718096; }

/* قسم الرسوم البيانية */
.charts-section {
    margin-bottom: 2rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.chart-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.chart-controls select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.875rem;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* قسم الأنشطة */
.activities-section {
    margin-bottom: 2rem;
}

.activities-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.recent-activities-card,
.notifications-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-badge {
    background: #f56565;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin-right: 0.5rem;
}

/* عناصر الأنشطة */
.activities-list,
.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item,
.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
}

.activity-item:hover,
.notification-item:hover {
    background: #f7fafc;
}

.activity-icon,
.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-item.prosthesis-added .activity-icon {
    background: #c6f6d5;
    color: #38a169;
}

.activity-item.prosthesis-completed .activity-icon {
    background: #bee3f8;
    color: #3182ce;
}

.activity-item.prosthesis-overdue .activity-icon {
    background: #fed7d7;
    color: #e53e3e;
}

.notification-item.error .notification-icon {
    background: #fed7d7;
    color: #e53e3e;
}

.notification-item.warning .notification-icon {
    background: #fef5e7;
    color: #d69e2e;
}

.notification-item.info .notification-icon {
    background: #bee3f8;
    color: #3182ce;
}

.activity-content,
.notification-content {
    flex: 1;
}

.activity-title,
.notification-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-description,
.notification-message {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0aec0;
    font-size: 0.75rem;
}

.activity-urgent {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: 8px;
    height: 8px;
    background: #f56565;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.notification-item.urgent {
    border-right: 4px solid #f56565;
}

.notification-action {
    margin-left: 0.5rem;
}

.notification-close {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 0.75rem;
    color: #718096;
}

.notification-item:hover .notification-close {
    opacity: 1;
}

.notification-close:hover {
    background: #cbd5e0;
}

/* الملخص السريع */
.quick-summary-section {
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.summary-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.summary-content {
    flex: 1;
}

.summary-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.summary-label {
    color: #718096;
    font-size: 0.875rem;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #a0aec0;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 1024px) {
    .activities-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .enhanced-dashboard {
        padding: 1rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-title {
        font-size: 2rem;
    }
}

/* تأثيرات الحركة */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* شريط التمرير المخصص */
.activities-list::-webkit-scrollbar,
.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.activities-list::-webkit-scrollbar-track,
.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb,
.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb:hover,
.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
