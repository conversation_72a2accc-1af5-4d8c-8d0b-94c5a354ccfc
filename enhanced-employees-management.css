/**
 * أنماط وحدة إدارة الموظفين المحسنة
 * Enhanced Employees Management Styles
 */

/* الحاوي الرئيسي */
.enhanced-employees-management {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    direction: rtl;
}

/* رأس الوحدة */
.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    flex-wrap: wrap;
    gap: 1rem;
}

.module-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.module-title i {
    color: #667eea;
}

.module-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* إحصائيات الموظفين */
.employees-stats {
    margin-bottom: 2rem;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* التبويبات الرئيسية */
.module-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e2e8f0;
    background: #f7fafc;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #718096;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
    justify-content: center;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.tab-btn:hover {
    color: #4a5568;
    background: rgba(255, 255, 255, 0.5);
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* البحث والفلاتر */
.search-filters-section {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-bar {
    flex: 1;
    min-width: 300px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-group i {
    position: absolute;
    right: 1rem;
    color: #a0aec0;
    font-size: 1.1rem;
}

.search-input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filters-group select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filters-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* شبكة الموظفين */
.employees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 1.5rem;
}

/* بطاقة الموظف */
.employee-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.employee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--status-color);
}

.employee-card.active { --status-color: #48bb78; }
.employee-card.inactive { --status-color: #a0aec0; }
.employee-card.suspended { --status-color: #f56565; }
.employee-card.on_leave { --status-color: #ed8936; }

.employee-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* رأس بطاقة الموظف */
.employee-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.employee-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
    overflow: hidden;
}

.employee-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.active { background: #48bb78; }
.status-indicator.inactive { background: #a0aec0; }
.status-indicator.suspended { background: #f56565; }
.status-indicator.on_leave { background: #ed8936; }

.employee-basic-info {
    flex: 1;
}

.employee-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.25rem 0;
}

.employee-position {
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0 0 0.25rem 0;
}

.employee-department {
    color: #718096;
    font-size: 0.85rem;
    margin: 0 0 0.25rem 0;
}

.employee-id {
    color: #a0aec0;
    font-size: 0.75rem;
    font-weight: 500;
}

.employee-status {
    align-self: flex-start;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #c6f6d5;
    color: #38a169;
}

.status-badge.inactive {
    background: #e2e8f0;
    color: #718096;
}

.status-badge.suspended {
    background: #fed7d7;
    color: #e53e3e;
}

.status-badge.on_leave {
    background: #fef5e7;
    color: #d69e2e;
}

/* إحصائيات الموظف */
.employee-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-item i {
    display: block;
    font-size: 1.2rem;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: #718096;
}

/* معلومات الاتصال */
.employee-contact {
    margin-bottom: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #4a5568;
}

.contact-item i {
    color: #a0aec0;
    width: 16px;
    text-align: center;
}

/* إجراءات الموظف */
.employee-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    position: relative;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-width: 180px;
    z-index: 1000;
    display: none;
    padding: 0.5rem 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.dropdown-menu a:hover {
    background: #f7fafc;
}

.dropdown-menu a.text-danger {
    color: #e53e3e;
}

.dropdown-menu a.text-warning {
    color: #d69e2e;
}

.dropdown-menu a.text-success {
    color: #38a169;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 0.5rem 0;
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    white-space: nowrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-1px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-1px);
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
    transform: translateY(-1px);
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #a0aec0;
    grid-column: 1 / -1;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    color: #e2e8f0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #718096;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 1024px) {
    .employees-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .enhanced-employees-management {
        padding: 1rem;
    }
    
    .module-header {
        flex-direction: column;
        text-align: center;
    }
    
    .search-filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        min-width: auto;
    }
    
    .filters-group {
        justify-content: center;
    }
    
    .employees-grid {
        grid-template-columns: 1fr;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        min-width: auto;
        flex: 1;
    }
    
    .employee-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .module-title {
        font-size: 2rem;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .employee-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        text-align: right;
    }
    
    .stat-item i {
        margin-bottom: 0;
    }
}
