<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة المنسدلة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        /* Navigation styles */
        #main-nav {
            background: linear-gradient(135deg, #0d47a1, #1565c0);
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        #main-nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        #main-nav li {
            margin: 0;
        }

        #main-nav a {
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        #main-nav a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Dropdown menu styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .dropdown-arrow {
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .dropdown.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 250px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            list-style: none;
            margin: 0;
            padding: 0.5rem 0;
            display: block !important;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu li {
            margin: 0;
        }

        .dropdown-menu a {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0;
            font-size: 0.95rem;
        }

        .dropdown-menu a:hover {
            background: #f8f9fa;
            color: #0d47a1;
            padding-right: 2rem;
            transform: none;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #0d47a1;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار القائمة المنسدلة</h1>
        
        <!-- Test Navigation -->
        <nav id="main-nav">
            <ul>
                <li><a href="#" onclick="showStatus('تم النقر على لوحة التحكم', 'success')">🏠 لوحة التحكم</a></li>
                
                <!-- Dropdown Menu for Work Registration -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle" onclick="toggleDropdown(event, 'work-dropdown')">
                        🦷 تسجيل العمل <span class="dropdown-arrow">▼</span>
                    </a>
                    <ul class="dropdown-menu" id="work-dropdown">
                        <li><a href="#" onclick="showStatus('تم النقر على تسجيل التركيبات', 'success')">➕ تسجيل التركيبات</a></li>
                        <li><a href="#" onclick="showStatus('تم النقر على قائمة التركيبات المسجلة', 'success')">📋 قائمة التركيبات المسجلة</a></li>
                    </ul>
                </li>
                
                <li><a href="#" onclick="showStatus('تم النقر على إدارة الأطباء', 'success')">👨‍⚕️ إدارة الأطباء</a></li>
                <li><a href="#" onclick="showStatus('تم النقر على تسجيل الخروج', 'error')">🚪 تسجيل الخروج</a></li>
            </ul>
        </nav>
        
        <!-- Test Buttons -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn btn-primary" onclick="testDropdownVisibility()">
                🔍 فحص القائمة المنسدلة
            </button>
            <button class="btn btn-success" onclick="forceShowDropdown()">
                👁️ إظهار القائمة بالقوة
            </button>
            <button class="btn btn-info" onclick="inspectDropdown()">
                🔧 فحص تفصيلي
            </button>
        </div>
        
        <!-- Status Display -->
        <div id="status-display"></div>
        
        <!-- Instructions -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px;">
            <h3>📋 تعليمات الاختبار:</h3>
            <ol>
                <li><strong>انقر على "🦷 تسجيل العمل"</strong> - يجب أن تظهر القائمة المنسدلة</li>
                <li><strong>انقر على أحد عناصر القائمة</strong> - يجب أن تظهر رسالة نجاح</li>
                <li><strong>انقر خارج القائمة</strong> - يجب أن تختفي القائمة</li>
                <li><strong>استخدم أزرار الاختبار</strong> - للتشخيص التفصيلي</li>
            </ol>
        </div>
    </div>

    <script>
        // Toggle dropdown menu
        function toggleDropdown(event, dropdownId) {
            event.preventDefault();
            event.stopPropagation();
            
            console.log('🔽 toggleDropdown called for:', dropdownId);
            
            const dropdown = document.getElementById(dropdownId);
            console.log('📋 Dropdown element found:', dropdown);
            
            if (!dropdown) {
                showStatus('❌ لم يتم العثور على القائمة المنسدلة: ' + dropdownId, 'error');
                return;
            }
            
            const allDropdowns = document.querySelectorAll('.dropdown-menu');
            console.log('📋 All dropdowns found:', allDropdowns.length);
            
            // Close all other dropdowns
            allDropdowns.forEach(menu => {
                if (menu.id !== dropdownId) {
                    menu.classList.remove('show');
                }
            });
            
            // Toggle current dropdown
            const isShowing = dropdown.classList.contains('show');
            dropdown.classList.toggle('show');
            
            console.log('📋 Dropdown toggled. Now showing:', !isShowing);
            console.log('📋 Dropdown classes:', dropdown.className);
            
            showStatus(
                !isShowing ? '✅ تم فتح القائمة المنسدلة' : '❌ تم إغلاق القائمة المنسدلة', 
                !isShowing ? 'success' : 'error'
            );
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
        
        // Test functions
        function testDropdownVisibility() {
            const dropdown = document.getElementById('work-dropdown');
            if (dropdown) {
                const isVisible = dropdown.classList.contains('show');
                const computedStyle = window.getComputedStyle(dropdown);
                
                showStatus(`
                    📋 حالة القائمة المنسدلة:
                    - موجودة: ✅
                    - فئة 'show': ${isVisible ? '✅' : '❌'}
                    - الشفافية: ${computedStyle.opacity}
                    - الرؤية: ${computedStyle.visibility}
                    - التحويل: ${computedStyle.transform}
                `, isVisible ? 'success' : 'error');
            } else {
                showStatus('❌ القائمة المنسدلة غير موجودة!', 'error');
            }
        }
        
        function forceShowDropdown() {
            const dropdown = document.getElementById('work-dropdown');
            if (dropdown) {
                dropdown.classList.add('show');
                dropdown.style.opacity = '1';
                dropdown.style.visibility = 'visible';
                dropdown.style.transform = 'translateY(0)';
                showStatus('✅ تم إظهار القائمة بالقوة', 'success');
            } else {
                showStatus('❌ لم يتم العثور على القائمة', 'error');
            }
        }
        
        function inspectDropdown() {
            const dropdown = document.getElementById('work-dropdown');
            if (dropdown) {
                console.log('🔍 Dropdown inspection:');
                console.log('- Element:', dropdown);
                console.log('- HTML:', dropdown.innerHTML);
                console.log('- Classes:', dropdown.className);
                console.log('- Style:', dropdown.style.cssText);
                console.log('- Computed style:', window.getComputedStyle(dropdown));
                
                showStatus('🔍 تم فحص القائمة - راجع وحدة التحكم للتفاصيل', 'success');
            } else {
                showStatus('❌ لم يتم العثور على القائمة للفحص', 'error');
            }
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        console.log('🧪 Test page loaded successfully');
    </script>
</body>
</html>
